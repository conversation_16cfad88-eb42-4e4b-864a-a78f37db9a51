# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="Missing Log Imputation - Petrel Interactive",
                                      category="Well log analysis",
                                      description="Interactive workflow for missing well log imputation using multiple ML algorithms. Select wells and logs directly from the current Petrel project, choose target log for imputation, and compare performance of different algorithms.",
                                      authors="BKP_Team@PTM",
                                      version="1.1") # Version updated

# --- Define the UI for the Prizm Workflow Runner ---

pwr_description.add_object_ref_parameter(name='index_well_id', label='Index Well (Reference)', description='Select a reference well with complete log data for training', object_type=DomainObjectsEnum.Well)
pwr_description.add_object_ref_parameter(name='input_wells_ids', label='Input Wells', description='Select wells to include in the analysis (including wells with missing data)', object_type=DomainObjectsEnum.Well, select_multiple=True)
pwr_description.add_object_ref_parameter(name='input_log_ids', label='Input Logs', description='Select input logs for features (e.g., GR, RHOB, NPHI, Vp)', object_type=DomainObjectsEnum.WellContinuousLog, select_multiple=True, linked_input_name='index_well_id')
pwr_description.add_object_ref_parameter(name='target_log_id', label='Target Log', description='Select the target log to impute (e.g., Vs)', object_type=DomainObjectsEnum.WellContinuousLog, linked_input_name='index_well_id')
pwr_description.add_enum_parameter(name='imputation_mode', label='Imputation Mode', description='Choose the imputation strategy', options={0:'Missing Values Only', 1:'Cross-Validation Test', 2:'Full Re-prediction'}, default_value=0)
pwr_description.add_float_parameter(name='test_percentage', label='Test Data Percentage', description='Percentage of complete data to use for testing (Cross-validation mode)', default_value=25.0, minimum_value=10.0, maximum_value=50.0)
pwr_description.add_boolean_parameter(name='use_depth_feature', label='Include Depth as Feature', description='Use measured depth (MD) as an additional feature for prediction', default_value=True)
pwr_description.add_boolean_parameter(name='enable_advanced_models', label='Enable Advanced Models', description='Include XGBoost, LightGBM, and CatBoost (requires additional packages)', default_value=True)
pwr_description.add_boolean_parameter(name='enable_tpot', label='Enable TPOT AutoML', description='Use TPOT for automated machine learning pipeline optimization (Windows-compatible)', default_value=False)
pwr_description.add_integer_parameter(name='tpot_generations', label='TPOT Generations', description='Number of generations for TPOT evolution (more = better but slower)', default_value=5, minimum_value=1, maximum_value=20)
pwr_description.add_integer_parameter(name='tpot_population_size', label='TPOT Population Size', description='Population size for TPOT evolution', default_value=20, minimum_value=10, maximum_value=100)
pwr_description.add_boolean_parameter(name='cross_validation', label='Use Cross Validation', description='Perform k-fold cross validation for model evaluation', default_value=True)
pwr_description.add_integer_parameter(name='cv_folds', label='CV Folds', description='Number of cross-validation folds', default_value=5, minimum_value=3, maximum_value=10)
pwr_description.add_boolean_parameter(name='create_output_logs', label='Create Output Logs', description='Create new well logs in Petrel with imputed values', default_value=True)
pwr_description.add_string_parameter(name='output_suffix', label='Output Log Suffix', description='Suffix to add to output log names (e.g., "_imputed")', default_value='_ML_imputed')

# End: PWR Description

# Ensure the parameters dictionary is available for debugging
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    print("DEBUG: Using default parameters for testing")
    print(f"DEBUG: Default parameters: {parameters}")
else:
    print(f"DEBUG: Using provided parameters: {parameters}")

# Core libraries
import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Any
import time
from datetime import datetime
warnings.filterwarnings('ignore')

# Plotting libraries not available in Petrel pythontool environment

# Machine learning libraries
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer

# Advanced ML libraries (optional)
try:
    from xgboost import XGBRegressor
    XGBOOST_AVAILABLE = True
    print("DEBUG: XGBoost library loaded successfully")
except ImportError:
    XGBOOST_AVAILABLE = False
    print("WARNING: XGBoost not available. Install with: pip install xgboost")

try:
    from lightgbm import LGBMRegressor
    LIGHTGBM_AVAILABLE = True
    print("DEBUG: LightGBM library loaded successfully")
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("WARNING: LightGBM not available. Install with: pip install lightgbm")

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
    print("DEBUG: CatBoost library loaded successfully")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("WARNING: CatBoost not available. Install with: pip install catboost")

# TPOT for automated machine learning (Windows-compatible alternative to auto-sklearn)
try:
    from tpot import TPOTRegressor
    TPOT_AVAILABLE = True
    print("DEBUG: TPOT library loaded successfully")
except ImportError:
    TPOT_AVAILABLE = False
    print("WARNING: TPOT not available. Install with: pip install tpot")

# Petrel connection
try:
    from cegalprizm.pythontool import PetrelConnection, Well, GlobalWellLog, WellLog, DiscreteGlobalWellLog
    PETREL_AVAILABLE = True
    print("DEBUG: Petrel connection libraries loaded successfully")
except ImportError:
    PETREL_AVAILABLE = False
    print("ERROR: Petrel connection not available. Running in standalone mode.")


class PetrelDataLoader:
    """Load and process well log data from Petrel project"""

    def __init__(self, petrel_connection: PetrelConnection):
        print("DEBUG: Initializing PetrelDataLoader")
        self.petrel = petrel_connection
        self.wells_data = {}
        self.combined_data = None
        print("DEBUG: PetrelDataLoader initialized successfully")

    def load_wells_and_logs(self, well_ids: List[str], log_names: List[str],
                           target_log_name: str, use_depth: bool = True) -> pd.DataFrame:
        """Load well log data from selected wells and logs"""

        print("Loading data from Petrel project...")
        print(f"DEBUG: Input parameters - well_ids count: {len(well_ids)}, log_names: {log_names}")
        print(f"DEBUG: Target log name: {target_log_name}, use_depth: {use_depth}")

        try:
            # Get well objects
            print("DEBUG: Retrieving well objects from Petrel")
            wells = self.petrel.get_petrelobjects_by_guids(well_ids)
            wells = [obj for obj in wells if isinstance(obj, Well)]

            if not wells:
                raise ValueError("No valid wells selected")

            print(f"  Selected wells: {len(wells)}")
            print(f"DEBUG: Well names: {[well.petrel_name for well in wells]}")

            # Get global well logs by name
            all_log_names = log_names + [target_log_name]
            print(f"DEBUG: All log names to retrieve: {all_log_names}")
            all_logs = []

            # Helper function to find global well log by name (following Vsh_calculator.py pattern)
            def find_global_well_log_by_name(name):
                for item in self.petrel.global_well_logs:
                    # Handle both individual objects and lists of objects
                    if isinstance(item, list):
                        for obj in item:
                            if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                                return obj
                    else:
                        if hasattr(item, 'petrel_name') and item.petrel_name == name:
                            return item
                return None

            print("DEBUG: Searching for global well logs using established pattern")
            for log_name in all_log_names:
                print(f"DEBUG: Searching for global log: {log_name}")
                found_log = find_global_well_log_by_name(log_name)
                if found_log:
                    all_logs.append(found_log)
                    print(f"  ✓ Found global log: {log_name}")
                    print(f"DEBUG: Global log type: {type(found_log)}")
                else:
                    print(f"  ✗ WARNING: Global log '{log_name}' not found")

            if not all_logs:
                raise ValueError("No valid global well logs found")

            print(f"  Using {len(all_logs)} global logs: {[log.petrel_name for log in all_logs]}")
            print(f"DEBUG: Successfully retrieved {len(all_logs)} global logs")

        except Exception as e:
            print(f"ERROR: Failed to retrieve wells and logs from Petrel: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: well_ids type: {type(well_ids)}, length: {len(well_ids) if hasattr(well_ids, '__len__') else 'No length'}")
            raise

        # Load data from each well
        all_well_data = []
        print("DEBUG: Starting data loading from individual wells")

        for well in wells:
            print(f"  Loading data from well: {well.petrel_name}")
            print(f"DEBUG: Processing well: {well.petrel_name} (type: {type(well)})")

            try:
                # Get logs dataframe for this well
                print(f"DEBUG: Calling logs_dataframe for well {well.petrel_name}")
                well_df = well.logs_dataframe(all_logs)

                if well_df.empty:
                    print(f"    WARNING: No data found for well {well.petrel_name}")
                    continue

                print(f"DEBUG: Retrieved dataframe shape: {well_df.shape} for well {well.petrel_name}")
                print(f"DEBUG: Original columns: {list(well_df.columns)}")

                # Add well identifier
                well_df['WELL'] = well.petrel_name

                # Reset index to make MD a column
                well_df = well_df.reset_index()
                print(f"DEBUG: After reset_index, columns: {list(well_df.columns)}")

                # Rename columns to match log names
                log_name_mapping = {}
                for i, log in enumerate(all_logs):
                    if i < len(well_df.columns) - 2:  # Exclude WELL and MD columns
                        old_name = well_df.columns[i]
                        log_name_mapping[old_name] = log.petrel_name

                print(f"DEBUG: Column mapping for {well.petrel_name}: {log_name_mapping}")
                well_df = well_df.rename(columns=log_name_mapping)
                print(f"DEBUG: Final columns for {well.petrel_name}: {list(well_df.columns)}")

                all_well_data.append(well_df)
                print(f"DEBUG: Successfully processed well {well.petrel_name} with {len(well_df)} samples")

            except Exception as e:
                print(f"    ERROR: Error loading data from well {well.petrel_name}: {str(e)}")
                print(f"DEBUG: Exception type for well {well.petrel_name}: {type(e)}")
                continue

        if not all_well_data:
            raise ValueError("No data could be loaded from any wells")

        print(f"DEBUG: Successfully loaded data from {len(all_well_data)} wells")

        try:
            # Combine all well data
            print("DEBUG: Combining data from all wells")
            combined_df = pd.concat(all_well_data, ignore_index=True)
            print(f"DEBUG: Combined dataframe shape: {combined_df.shape}")
            print(f"DEBUG: Combined dataframe columns: {list(combined_df.columns)}")

            # Clean up column names and ensure we have the expected columns
            expected_columns = ['MD', 'WELL'] + [log.petrel_name for log in all_logs]
            print(f"DEBUG: Expected columns: {expected_columns}")

            # Filter to only include expected columns that exist
            available_columns = [col for col in expected_columns if col in combined_df.columns]
            print(f"DEBUG: Available columns: {available_columns}")

            missing_columns = [col for col in expected_columns if col not in combined_df.columns]
            if missing_columns:
                print(f"WARNING: Missing expected columns: {missing_columns}")

            combined_df = combined_df[available_columns]

            # Remove depth feature if not requested
            if not use_depth and 'MD' in combined_df.columns:
                print("DEBUG: Removing MD column as depth feature not requested")
                combined_df = combined_df.drop('MD', axis=1)

            print(f"  Total samples loaded: {len(combined_df)}")
            print(f"  Available columns: {list(combined_df.columns)}")
            print(f"DEBUG: Final combined data shape: {combined_df.shape}")

            # Store for later use
            self.combined_data = combined_df
            print("DEBUG: Data loading completed successfully")

            return combined_df

        except Exception as e:
            print(f"ERROR: Failed to combine well data: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: Number of well dataframes to combine: {len(all_well_data)}")
            if all_well_data:
                print(f"DEBUG: Sample dataframe shapes: {[df.shape for df in all_well_data[:3]]}")
            raise

    def get_data_coverage_stats(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate data coverage statistics"""

        print("DEBUG: Calculating data coverage statistics")
        coverage_stats = {}
        log_columns = [col for col in df.columns if col not in ['WELL', 'MD']]
        print(f"DEBUG: Log columns for coverage analysis: {log_columns}")

        try:
            for col in log_columns:
                if col not in df.columns:
                    print(f"WARNING: Column {col} not found in dataset")
                    continue
                coverage = (1 - df[col].isna().mean()) * 100
                coverage_stats[col] = coverage
                missing_count = df[col].isna().sum()
                total_count = len(df[col])
                print(f"DEBUG: {col} coverage: {coverage:.1f}% ({missing_count}/{total_count} missing)")

            print(f"DEBUG: Coverage statistics calculated for {len(coverage_stats)} columns")
            return coverage_stats

        except Exception as e:
            print(f"ERROR: Failed to calculate coverage statistics: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: DataFrame type: {type(df)}")
            print(f"DEBUG: DataFrame columns: {list(df.columns) if hasattr(df, 'columns') else 'No columns attribute'}")
            raise

    def identify_index_well_data(self, df: pd.DataFrame, index_well_id: str,
                                target_log_name: str) -> pd.DataFrame:
        """Get data from the index well for training"""

        print("DEBUG: Identifying index well data")
        print(f"DEBUG: Index well ID: {index_well_id}")
        print(f"DEBUG: Target log name: {target_log_name}")

        try:
            # Get index well object
            print("DEBUG: Retrieving index well object from Petrel")
            index_well = self.petrel.get_petrelobjects_by_guids([index_well_id])[0]
            index_well_name = index_well.petrel_name

            print(f"Using index well: {index_well_name}")
            print(f"DEBUG: Index well type: {type(index_well)}")

            # Filter data for index well
            print("DEBUG: Filtering data for index well")
            index_data = df[df['WELL'] == index_well_name].copy()

            if index_data.empty:
                raise ValueError(f"No data found for index well: {index_well_name}")

            print(f"DEBUG: Index well data shape: {index_data.shape}")

            # Check if index well has complete target log data
            print("DEBUG: Checking target log coverage in index well")
            target_missing = index_data[target_log_name].isna().sum()
            target_total = len(index_data)
            coverage_percentage = ((target_total - target_missing) / target_total * 100)

            print(f"  Index well {target_log_name} coverage: {coverage_percentage:.1f}%")
            print(f"DEBUG: Target log - missing: {target_missing}, total: {target_total}")

            if target_missing > target_total * 0.5:
                print(f"  WARNING: Index well has >50% missing {target_log_name} data")

            print("DEBUG: Index well data identification completed successfully")
            return index_data

        except Exception as e:
            print(f"ERROR: Failed to identify index well data: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: Available wells in dataset: {df['WELL'].unique().tolist() if 'WELL' in df.columns else 'WELL column not found'}")
            raise


class PetrelMLImputationFramework:
    """ML framework adapted for Petrel data with TPOT AutoML support"""

    def __init__(self, enable_advanced_models: bool = True, enable_tpot: bool = False,
                 tpot_generations: int = 5, tpot_population_size: int = 20, random_seed: int = 42):
        print("DEBUG: Initializing PetrelMLImputationFramework")
        print(f"DEBUG: Parameters - advanced_models: {enable_advanced_models}, tpot: {enable_tpot}")
        print(f"DEBUG: TPOT parameters - generations: {tpot_generations}, population: {tpot_population_size}")
        print(f"DEBUG: Random seed: {random_seed}")

        self.enable_advanced_models = enable_advanced_models
        self.enable_tpot = enable_tpot
        self.tpot_generations = tpot_generations
        self.tpot_population_size = tpot_population_size
        self.random_seed = random_seed
        self.models = {}
        self.results = {}
        self.tpot_model = None

        print("DEBUG: PetrelMLImputationFramework initialized successfully")

    def get_available_models(self) -> Dict[str, Any]:
        """Get dictionary of available ML models"""

        print("DEBUG: Getting available ML models")

        models = {
            'Linear_Regression': LinearRegression(),
            'Ridge_Regression': Ridge(alpha=1.0, random_state=self.random_seed),
            'Lasso_Regression': Lasso(alpha=1.0, random_state=self.random_seed),
            'Random_Forest': RandomForestRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Extra_Trees': ExtraTreesRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Gradient_Boosting': GradientBoostingRegressor(n_estimators=100, random_state=self.random_seed)
        }

        print(f"DEBUG: Base models defined: {list(models.keys())}")

        # Add advanced models if available and enabled
        if self.enable_advanced_models:
            print("DEBUG: Advanced models enabled, checking availability")

            if XGBOOST_AVAILABLE:
                models['XGBoost'] = XGBRegressor(n_estimators=300, learning_rate=0.05,
                                               random_state=self.random_seed, n_jobs=-1)
                print("DEBUG: XGBoost model added")

            if LIGHTGBM_AVAILABLE:
                models['LightGBM'] = LGBMRegressor(n_estimators=300, random_state=self.random_seed, n_jobs=-1)
                print("DEBUG: LightGBM model added")

            if CATBOOST_AVAILABLE:
                models['CatBoost'] = CatBoostRegressor(iterations=300, learning_rate=0.05,
                                                     random_state=self.random_seed, verbose=False)
                print("DEBUG: CatBoost model added")
        else:
            print("DEBUG: Advanced models disabled")

        # Add TPOT AutoML if available and enabled
        if self.enable_tpot and TPOT_AVAILABLE:
            print("DEBUG: TPOT AutoML enabled and available, adding to models")
            models['TPOT_AutoML'] = TPOTRegressor(
                generations=self.tpot_generations,
                population_size=self.tpot_population_size,
                random_state=self.random_seed,
                scoring='neg_mean_absolute_error',
                cv=3,  # Internal CV for TPOT
                n_jobs=-1,
                verbosity=1,
                config_dict='TPOT light'  # Use lighter config for faster execution
            )
            print("DEBUG: TPOT AutoML model added")
        elif self.enable_tpot and not TPOT_AVAILABLE:
            print("WARNING: TPOT enabled but not available")
        else:
            print("DEBUG: TPOT AutoML disabled")

        print(f"DEBUG: Total models available: {len(models)} - {list(models.keys())}")
        return models

    def train_tpot_model(self, X_train, y_train, X_test, y_test, feature_cols: List[str]) -> Dict[str, Any]:
        """Train TPOT AutoML model separately due to its different workflow"""

        if not TPOT_AVAILABLE:
            print("ERROR: TPOT not available for training")
            return {'error': 'TPOT not available'}

        print(f"  Training TPOT AutoML (Generations: {self.tpot_generations}, Population: {self.tpot_population_size})...")
        print(f"    This may take several minutes as TPOT searches for optimal pipeline...")
        print(f"DEBUG: TPOT training parameters - generations: {self.tpot_generations}, population: {self.tpot_population_size}")
        print(f"DEBUG: Training data shape - X_train: {X_train.shape}, y_train: {len(y_train)}")
        print(f"DEBUG: Test data shape - X_test: {X_test.shape}, y_test: {len(y_test)}")

        start_time = time.time()

        try:
            # Create TPOT regressor
            print("DEBUG: Creating TPOT regressor instance")
            tpot = TPOTRegressor(
                generations=self.tpot_generations,
                population_size=self.tpot_population_size,
                random_state=self.random_seed,
                scoring='neg_mean_absolute_error',
                cv=3,
                n_jobs=-1,
                verbosity=1,
                config_dict='TPOT light'
            )
            print("DEBUG: TPOT regressor created successfully")

            # Fit TPOT
            print("DEBUG: Starting TPOT fit process")
            tpot.fit(X_train, y_train)
            print("DEBUG: TPOT fit completed")

            # Store the fitted TPOT model
            self.tpot_model = tpot
            print("DEBUG: TPOT model stored in framework")

            # Make predictions
            print("DEBUG: Making predictions with TPOT model")
            y_pred_train = tpot.predict(X_train)
            y_pred_test = tpot.predict(X_test)
            print("DEBUG: TPOT predictions completed")

            # Calculate metrics
            print("DEBUG: Calculating TPOT performance metrics")
            train_mae = mean_absolute_error(y_train, y_pred_train)
            test_mae = mean_absolute_error(y_test, y_pred_test)
            train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
            train_r2 = r2_score(y_train, y_pred_train)
            test_r2 = r2_score(y_test, y_pred_test)

            training_time = time.time() - start_time

            # Get the best pipeline info
            print("DEBUG: Extracting TPOT best pipeline information")
            best_pipeline = str(tpot.fitted_pipeline_)

            print(f"    TPOT completed! Best pipeline: {best_pipeline[:100]}...")
            print(f"    Test MAE: {test_mae:.3f}, Test R²: {test_r2:.3f}, Time: {training_time:.2f}s")
            print(f"DEBUG: TPOT training completed successfully in {training_time:.2f} seconds")

            return {
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'cv_mae': None,  # TPOT does internal CV
                'cv_std': None,
                'training_time': training_time,
                'model': tpot,
                'imputer': SimpleImputer(strategy='mean'),  # TPOT handles preprocessing internally
                'best_pipeline': best_pipeline
            }

        except Exception as e:
            print(f"    ERROR: Error training TPOT: {str(e)}")
            print(f"DEBUG: TPOT exception type: {type(e)}")
            print(f"DEBUG: TPOT training time before error: {time.time() - start_time:.2f}s")
            if hasattr(tpot, 'fitted_pipeline_'):
                print(f"DEBUG: Fitted pipeline available: {tpot.fitted_pipeline_}")
            else:
                print(f"DEBUG: No fitted pipeline available")
            return {'error': str(e)}

    def train_and_evaluate_models(self, df: pd.DataFrame, target_col: str,
                                 feature_cols: List[str], test_size: float = 0.25,
                                 use_cv: bool = True, cv_folds: int = 5) -> Dict[str, Dict[str, float]]:
        """Train and evaluate all available models"""

        print(f"Training and evaluating models for {target_col} imputation...")
        print(f"DEBUG: Input parameters - target_col: {target_col}, test_size: {test_size}")
        print(f"DEBUG: CV parameters - use_cv: {use_cv}, cv_folds: {cv_folds}")
        print(f"DEBUG: Feature columns: {feature_cols}")

        try:
            # Prepare data
            print("DEBUG: Preparing data for model training")
            complete_mask = df[target_col].notna()
            train_data = df[complete_mask].copy()

            print(f"DEBUG: Complete data samples: {len(train_data)} out of {len(df)} total")

            if len(train_data) < 10:
                raise ValueError(f"Insufficient complete data for {target_col} (only {len(train_data)} samples)")

            X = train_data[feature_cols].copy()
            y = train_data[target_col].values

            print(f"DEBUG: Feature matrix shape: {X.shape}")
            print(f"DEBUG: Target vector length: {len(y)}")

            # Handle missing values in features using mean imputation
            print("DEBUG: Applying mean imputation to features")
            imputer = SimpleImputer(strategy='mean')
            X_imputed = imputer.fit_transform(X)
            print(f"DEBUG: Imputed feature matrix shape: {X_imputed.shape}")

            # Split data
            print("DEBUG: Splitting data into train/test sets")
            X_train, X_test, y_train, y_test = train_test_split(
                X_imputed, y, test_size=test_size, random_state=self.random_seed
            )

            print(f"  Training samples: {len(X_train)}")
            print(f"  Test samples: {len(X_test)}")
            print(f"  Features: {feature_cols}")
            print(f"DEBUG: Data preparation completed successfully")
            print()

        except Exception as e:
            print(f"ERROR: Failed to prepare data for model training: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: DataFrame shape: {df.shape if hasattr(df, 'shape') else 'No shape'}")
            print(f"DEBUG: DataFrame columns: {list(df.columns) if hasattr(df, 'columns') else 'No columns'}")
            raise

        # Get available models
        print("DEBUG: Getting available models")
        models = self.get_available_models()
        results = {}

        # Handle TPOT separately if enabled
        if self.enable_tpot and TPOT_AVAILABLE and 'TPOT_AutoML' in models:
            print("DEBUG: TPOT AutoML enabled, training separately")
            tpot_results = self.train_tpot_model(X_train, y_train, X_test, y_test, feature_cols)
            results['TPOT_AutoML'] = tpot_results
            # Remove TPOT from regular models to avoid duplicate training
            models = {k: v for k, v in models.items() if k != 'TPOT_AutoML'}
            print("DEBUG: TPOT training completed, removed from regular models list")

        # Train regular models
        print(f"DEBUG: Training {len(models)} regular models")
        for model_name, model in models.items():
            print(f"  Training {model_name}...")
            print(f"DEBUG: Model type: {type(model)}")
            start_time = time.time()

            try:
                # Train model
                print(f"DEBUG: Fitting {model_name} with {len(X_train)} training samples")
                model.fit(X_train, y_train)
                print(f"DEBUG: {model_name} training completed")

                # Make predictions
                print(f"DEBUG: Making predictions with {model_name}")
                y_pred_train = model.predict(X_train)
                y_pred_test = model.predict(X_test)
                print(f"DEBUG: Predictions completed for {model_name}")

                # Calculate metrics
                print(f"DEBUG: Calculating metrics for {model_name}")
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)

                # Cross-validation if requested
                cv_scores = None
                if use_cv:
                    print(f"DEBUG: Performing {cv_folds}-fold cross-validation for {model_name}")
                    cv_scores = cross_val_score(model, X_train, y_train,
                                              cv=cv_folds, scoring='neg_mean_absolute_error')
                    cv_mae = -cv_scores.mean()
                    cv_std = cv_scores.std()
                    print(f"DEBUG: Cross-validation completed for {model_name}")
                else:
                    cv_mae = cv_std = None

                training_time = time.time() - start_time

                # Store results
                results[model_name] = {
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'cv_mae': cv_mae,
                    'cv_std': cv_std,
                    'training_time': training_time,
                    'model': model,
                    'imputer': imputer
                }

                print(f"    Test MAE: {test_mae:.3f}, Test R²: {test_r2:.3f}, Time: {training_time:.2f}s")
                if use_cv:
                    print(f"    CV MAE: {cv_mae:.3f} ± {cv_std:.3f}")
                print(f"DEBUG: {model_name} training and evaluation completed successfully")

            except Exception as e:
                print(f"    ERROR: Error training {model_name}: {str(e)}")
                print(f"DEBUG: Exception type for {model_name}: {type(e)}")
                print(f"DEBUG: Model parameters for {model_name}: {model.get_params() if hasattr(model, 'get_params') else 'No get_params method'}")
                results[model_name] = {'error': str(e)}

        print()
        print(f"DEBUG: Model training completed. {len(results)} models trained")
        self.results[target_col] = results
        return results


def main():
    """Main execution function"""

    print("="*80)
    print("MISSING LOG IMPUTATION - PETREL INTERACTIVE")
    print("="*80)
    print(f"Execution started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Ensure the parameters dictionary is available (following Vsh_calculator pattern)
    try:
        # Check if parameters exists in global scope and has valid content
        global_vars = globals()
        if 'parameters' in global_vars and global_vars['parameters'] is not None and isinstance(global_vars['parameters'], dict):
            # Use the global parameters
            parameters = global_vars['parameters']
            print(f"DEBUG: Using provided parameters from Petrel: {parameters}")
            print(f"DEBUG: Parameters type: {type(parameters)}")
            print(f"DEBUG: Parameters content: {parameters}")
        else:
            print("DEBUG: Parameters not found in global scope or invalid, using defaults")
            parameters = pwr_description.get_default_parameters()
            print("Using default parameters for testing")
            print(f"Default parameters: {parameters}")
            print(f"DEBUG: Default parameters type: {type(parameters)}")

    except Exception as e:
        print(f"ERROR: Failed to process parameters: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        raise

    # Connect to Petrel
    try:
        print("Establishing PetrelConnection")
        print("DEBUG: Creating PetrelConnection with allow_experimental=True")
        petrel = PetrelConnection(allow_experimental=True)
        project_name = petrel.get_current_project_name()
        print("Connected to {}".format(project_name))
        print(f"DEBUG: Successfully connected to Petrel project: {project_name}")
        print()

    except Exception as e:
        print(f"ERROR: Failed to establish Petrel connection: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        return

    # Get parameters
    try:
        print("DEBUG: Extracting parameters from parameter dictionary")
        index_well_id = parameters['index_well_id']
        input_wells_ids = parameters['input_wells_ids']
        input_log_ids = parameters['input_log_ids']
        target_log_id = parameters['target_log_id']

        print(f"DEBUG: index_well_id: {index_well_id}")
        print(f"DEBUG: input_wells_ids count: {len(input_wells_ids) if hasattr(input_wells_ids, '__len__') else 'No length'}")
        print(f"DEBUG: input_log_ids count: {len(input_log_ids) if hasattr(input_log_ids, '__len__') else 'No length'}")
        print(f"DEBUG: target_log_id: {target_log_id}")

    except Exception as e:
        print(f"ERROR: Failed to extract parameters: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Available parameter keys: {list(parameters.keys()) if hasattr(parameters, 'keys') else 'No keys method'}")
        return

    #############################
    # Checks for input data
    #############################

    # Retrieve the selected index well using its GUID and check if it's a valid Well object
    try:
        print("DEBUG: Retrieving index well object")
        index_well = petrel.get_petrelobjects_by_guids([index_well_id])[0]
        if not isinstance(index_well, Well):
            raise AssertionError("No index well selected or invalid well object")
        print("Retrieved index well by guid")
        print(f"DEBUG: Index well name: {index_well.petrel_name}")
        print(f"DEBUG: Index well type: {type(index_well)}")
    except Exception as e:
        print(f"ERROR: Error retrieving index well: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: index_well_id: {index_well_id}")
        return

    # Retrieve the selected input wells using their GUIDs and check if they are valid Well objects
    try:
        print("DEBUG: Retrieving input wells objects")
        input_wells = petrel.get_petrelobjects_by_guids(input_wells_ids)
        input_wells = [obj for obj in input_wells if isinstance(obj, Well)]
        if not input_wells:
            raise AssertionError("No valid input wells selected")
        print(f"Retrieved {len(input_wells)} input wells by guid")
        print(f"DEBUG: Input well names: {[well.petrel_name for well in input_wells]}")
    except Exception as e:
        print(f"ERROR: Error retrieving input wells: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: input_wells_ids: {input_wells_ids}")
        return

    # Get log names from selected log objects with enhanced validation
    try:
        print("DEBUG: Validating and retrieving input log objects")
        if not input_log_ids or not isinstance(input_log_ids, list):
            raise AssertionError(f"Invalid input_log_ids in parameters: {input_log_ids}")

        print(f"Attempting to retrieve {len(input_log_ids)} input logs")
        print(f"DEBUG: input_log_ids: {input_log_ids}")
        input_log_objects = petrel.get_petrelobjects_by_guids(input_log_ids)
        print(f"DEBUG: Retrieved {len(input_log_objects)} log objects")

        if not all(isinstance(log, WellLog) for log in input_log_objects):
            log_types = [type(log) for log in input_log_objects]
            print(f"DEBUG: Log object types: {log_types}")
            raise AssertionError("Not all selected input logs are valid WellLog objects.")

        input_log_names = [log.petrel_name for log in input_log_objects]
        print(f"Successfully retrieved input log names: {input_log_names}")
        print(f"DEBUG: Input log object types: {[type(log) for log in input_log_objects]}")

        print("DEBUG: Validating and retrieving target log object")
        if not target_log_id or not isinstance(target_log_id, str):
            raise AssertionError(f"Invalid target_log_id in parameters: {target_log_id}")

        print(f"Attempting to retrieve target log with GUID: {target_log_id}")
        target_log_object = petrel.get_petrelobjects_by_guids([target_log_id])[0]
        print(f"DEBUG: Target log object type: {type(target_log_object)}")

        if not isinstance(target_log_object, WellLog):
            raise AssertionError(f"Selected target object is not a WellLog. Got type: {type(target_log_object)}")

        target_log_name = target_log_object.petrel_name
        print(f"Successfully retrieved target log: {target_log_name}")
        print(f"DEBUG: Target log validation completed successfully")

    except Exception as e:
        print(f"ERROR: Error getting log names from selected objects: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Parameters: {parameters}")
        print(f"DEBUG: input_log_ids type: {type(input_log_ids) if 'input_log_ids' in locals() else 'Not defined'}")
        print(f"DEBUG: target_log_id type: {type(target_log_id) if 'target_log_id' in locals() else 'Not defined'}")
        return

    # Extract additional parameters
    try:
        print("DEBUG: Extracting additional configuration parameters")
        imputation_mode = parameters['imputation_mode']
        test_percentage = parameters['test_percentage']
        use_depth_feature = parameters['use_depth_feature']
        enable_advanced_models = parameters['enable_advanced_models']
        enable_tpot = parameters['enable_tpot']
        tpot_generations = parameters['tpot_generations']
        tpot_population_size = parameters['tpot_population_size']
        cross_validation = parameters['cross_validation']
        cv_folds = parameters['cv_folds']
        create_output_logs = parameters['create_output_logs']
        output_suffix = parameters['output_suffix']

        print("DEBUG: All configuration parameters extracted successfully")

    except Exception as e:
        print(f"ERROR: Failed to extract configuration parameters: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Available parameter keys: {list(parameters.keys()) if hasattr(parameters, 'keys') else 'No keys method'}")
        return

    # Debug: Check available libraries
    print("DEBUG: Checking available ML libraries...")
    print(f"  - XGBoost available: {XGBOOST_AVAILABLE}")
    print(f"  - LightGBM available: {LIGHTGBM_AVAILABLE}")
    print(f"  - CatBoost available: {CATBOOST_AVAILABLE}")
    print(f"  - TPOT available: {TPOT_AVAILABLE}")
    print(f"  - Petrel available: {PETREL_AVAILABLE}")
    print()

    print(f"Configuration:")
    print(f"  - Index well ID: {index_well_id}")
    print(f"  - Number of input wells: {len(input_wells_ids)}")
    print(f"  - Input log names: {input_log_names}")
    print(f"  - Target log name: {target_log_name}")
    print(f"  - Imputation mode: {['Missing Values Only', 'Cross-Validation Test', 'Full Re-prediction'][imputation_mode]}")
    print(f"  - Test percentage: {test_percentage}%")
    print(f"  - Use depth feature: {use_depth_feature}")
    print(f"  - Advanced models enabled: {enable_advanced_models}")
    print(f"  - TPOT enabled: {enable_tpot}")
    print(f"  - Cross validation: {cross_validation}")
    if cross_validation:
        print(f"  - CV folds: {cv_folds}")
    print()

    # Initialize data loader and load data
    try:
        print("DEBUG: Initializing PetrelDataLoader")
        data_loader = PetrelDataLoader(petrel)
        print("DEBUG: PetrelDataLoader initialized successfully")

        print("DEBUG: Starting data loading from Petrel")
        combined_data = data_loader.load_wells_and_logs(
            input_wells_ids, input_log_names, target_log_name, use_depth_feature
        )

        print("DEBUG: Defining feature columns")
        feature_cols = [col for col in combined_data.columns if col not in ['WELL', target_log_name]]
        print(f"DEBUG: Feature columns: {feature_cols}")
        print("DEBUG: Data loading completed successfully")

    except Exception as e:
        print(f"ERROR: Error loading data from Petrel: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Data loader state: {type(data_loader) if 'data_loader' in locals() else 'Not created'}")
        return

    # Initialize ML framework and train models
    try:
        print("DEBUG: Initializing PetrelMLImputationFramework")
        ml_framework = PetrelMLImputationFramework(
            enable_advanced_models=enable_advanced_models,
            enable_tpot=enable_tpot and TPOT_AVAILABLE,
            tpot_generations=tpot_generations,
            tpot_population_size=tpot_population_size
        )
        print("DEBUG: PetrelMLImputationFramework initialized successfully")

    except Exception as e:
        print(f"ERROR: Failed to initialize ML framework: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        return

    try:
        print("DEBUG: Starting model training and evaluation")
        print(f"DEBUG: Training parameters - test_size: {test_percentage/100.0}, cv: {cross_validation}, cv_folds: {cv_folds}")

        results = ml_framework.train_and_evaluate_models(
            combined_data, target_log_name, feature_cols,
            test_size=test_percentage/100.0,
            use_cv=cross_validation,
            cv_folds=cv_folds
        )

        print(f"DEBUG: Model training completed. {len(results)} models trained")

    except Exception as e:
        print(f"ERROR: Error training models: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Combined data shape: {combined_data.shape if hasattr(combined_data, 'shape') else 'No shape'}")
        print(f"DEBUG: Target log name: {target_log_name}")
        print(f"DEBUG: Feature columns: {feature_cols}")
        return

    # Print results summary and get best model
    try:
        print("DEBUG: Processing model results")
        print("Model Performance Summary")
        print("="*80)
        valid_results = {k: v for k, v in results.items() if 'error' not in v}

        print(f"DEBUG: Valid results count: {len(valid_results)} out of {len(results)} total")

        if not valid_results:
            print("ERROR: No valid model results to display. Exiting.")
            print(f"DEBUG: All results: {results}")
            return

        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['test_mae'])
        best_model_name, best_metrics = sorted_results[0]
        print(f"SELECTED MODEL: {best_model_name} (Lowest Test MAE: {best_metrics['test_mae']:.4f})")
        print("="*80)
        print(f"DEBUG: Best model selected: {best_model_name}")
        print(f"DEBUG: Best model metrics: {best_metrics}")

    except Exception as e:
        print(f"ERROR: Failed to process model results: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Results type: {type(results) if 'results' in locals() else 'Not defined'}")
        return

    # Perform imputation or prediction based on mode
    final_data = None
    final_suffix = ""

    try:
        print("DEBUG: Starting imputation/prediction process")
        print(f"DEBUG: Imputation mode: {imputation_mode}")

        if imputation_mode == 0:  # Missing Values Only
            print("IMPUTATION PROCESS - MISSING VALUES ONLY")
            print("DEBUG: Performing missing values only imputation")
            final_data = perform_imputation(
                combined_data, target_log_name, feature_cols,
                best_metrics['model'], best_metrics['imputer']
            )
            final_suffix = output_suffix
            print("DEBUG: Missing values imputation completed")

        elif imputation_mode == 2:  # Full Re-prediction
            print("FULL RE-PREDICTION PROCESS")
            print("DEBUG: Performing full re-prediction")
            final_data = perform_full_prediction(
                combined_data, target_log_name, feature_cols,
                best_metrics['model'], best_metrics['imputer']
            )
            final_suffix = output_suffix + "_predicted"
            print("DEBUG: Full re-prediction completed")

        else:
            print(f"WARNING: Imputation mode {imputation_mode} not implemented, skipping imputation")

        print(f"DEBUG: Final data shape: {final_data.shape if final_data is not None else 'None'}")
        print(f"DEBUG: Final suffix: '{final_suffix}'")

    except Exception as e:
        print(f"ERROR: Failed during imputation/prediction process: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Imputation mode: {imputation_mode}")
        print(f"DEBUG: Best model available: {'model' in best_metrics if 'best_metrics' in locals() else 'best_metrics not available'}")

    # Write back to Petrel if applicable
    if create_output_logs and final_data is not None:
        print(f"\nCreating output logs with suffix: '{final_suffix}'")
        print("DEBUG: Starting Petrel write-back process")
        try:
            # Find the target log to clone from
            print("DEBUG: Searching for target log to clone")
            def find_global_well_log_by_name(name):
                for item in petrel.global_well_logs:
                    if isinstance(item, list):
                        for obj in item:
                            if hasattr(obj, 'petrel_name') and obj.petrel_name == name: return obj
                    elif hasattr(item, 'petrel_name') and item.petrel_name == name: return item
                return None

            log_to_clone = find_global_well_log_by_name(target_log_name)
            if log_to_clone is None:
                raise AssertionError(f"Could not find global well log with name: {target_log_name}")

            print(f"DEBUG: Found log to clone: {log_to_clone.petrel_name}")

            # Find a suitable template
            print("DEBUG: Searching for suitable template")
            def find_template_by_name(name):
                for item in petrel.templates:
                    if isinstance(item, list):
                        for obj in item:
                            if hasattr(obj, 'petrel_name') and obj.petrel_name == name: return obj
                    elif hasattr(item, 'petrel_name') and item.petrel_name == name: return item
                return None

            template = find_template_by_name(target_log_name)
            if template is None:
                print("DEBUG: Target log template not found, searching for generic templates")
                for template_name in ['Continuous', 'WellLog', 'Log']:
                    template = find_template_by_name(template_name)
                    if template:
                        print(f"DEBUG: Found generic template: {template_name}")
                        break

            if template:
                print(f"Using template: {template.petrel_name}")
                print(f"DEBUG: Template type: {type(template)}")
            else:
                print("WARNING: No suitable template found, proceeding without.")

            print("DEBUG: Starting Petrel output log creation")
            create_petrel_output_logs_with_writeback(
                petrel, final_data, target_log_name,
                input_wells_ids, final_suffix, log_to_clone, template
            )
            print("DEBUG: Petrel write-back completed successfully")

        except Exception as e:
            print(f"ERROR: Error in write back preparation: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: final_data available: {final_data is not None}")
            print(f"DEBUG: target_log_name: {target_log_name}")

    print("\n" + "="*80)
    print("PETREL INTERACTIVE IMPUTATION COMPLETED")
    print(f"Execution completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("DEBUG: Main execution function completed successfully")
    print("="*80)


def perform_imputation(df: pd.DataFrame, target_col: str, feature_cols: List[str],
                      model: Any, imputer: SimpleImputer) -> pd.DataFrame:
    """Perform imputation on missing values only"""

    print("DEBUG: Starting missing values imputation")
    print(f"DEBUG: Input data shape: {df.shape}")
    print(f"DEBUG: Target column: {target_col}")
    print(f"DEBUG: Feature columns: {feature_cols}")

    try:
        df_imputed = df.copy()
        missing_mask = df_imputed[target_col].isna()

        print(f"DEBUG: Missing values count: {missing_mask.sum()}")

        if not missing_mask.any():
            print("✓ No missing values found to impute.")
            print("DEBUG: No imputation needed, returning original data")
            return df_imputed

        print("DEBUG: Preparing features for missing value prediction")
        X_missing = df_imputed.loc[missing_mask, feature_cols]
        print(f"DEBUG: Missing data features shape: {X_missing.shape}")

        print("DEBUG: Applying imputer transformation to features")
        X_missing_imputed = imputer.transform(X_missing)
        print(f"DEBUG: Imputed features shape: {X_missing_imputed.shape}")

        print("DEBUG: Making predictions for missing values")
        predictions = model.predict(X_missing_imputed)
        print(f"DEBUG: Predictions shape: {predictions.shape if hasattr(predictions, 'shape') else len(predictions)}")

        print("DEBUG: Assigning predictions to missing values")
        df_imputed.loc[missing_mask, target_col] = predictions

        print(f"✓ Successfully imputed {missing_mask.sum()} missing values for {target_col}")
        print("DEBUG: Missing values imputation completed successfully")
        return df_imputed

    except Exception as e:
        print(f"ERROR: Failed to perform imputation: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Model type: {type(model)}")
        print(f"DEBUG: Imputer type: {type(imputer)}")
        raise


def perform_full_prediction(df: pd.DataFrame, target_col: str, feature_cols: List[str],
                           model: Any, imputer: SimpleImputer) -> pd.DataFrame:
    """Perform full re-prediction of all values"""

    print("DEBUG: Starting full re-prediction")
    print(f"DEBUG: Input data shape: {df.shape}")
    print(f"DEBUG: Target column: {target_col}")
    print(f"DEBUG: Feature columns: {feature_cols}")

    try:
        df_predicted = df.copy()

        print("DEBUG: Preparing all features for prediction")
        X_all = df_predicted[feature_cols]
        print(f"DEBUG: All features shape: {X_all.shape}")

        print("DEBUG: Applying imputer transformation to all features")
        X_all_imputed = imputer.transform(X_all)
        print(f"DEBUG: Imputed all features shape: {X_all_imputed.shape}")

        print("DEBUG: Making predictions for all values")
        predictions = model.predict(X_all_imputed)
        print(f"DEBUG: All predictions shape: {predictions.shape if hasattr(predictions, 'shape') else len(predictions)}")

        # NOTE: The data is stored in a new column to be handled by the write-back function
        print("DEBUG: Storing predictions in new column")
        df_predicted[f'{target_col}_predicted'] = predictions

        print(f"✓ Successfully generated predictions for {len(df_predicted)} samples into new column.")
        print("DEBUG: Full re-prediction completed successfully")
        return df_predicted

    except Exception as e:
        print(f"ERROR: Failed to perform full prediction: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Model type: {type(model)}")
        print(f"DEBUG: Imputer type: {type(imputer)}")
        raise


def write_back_log(petrel: PetrelConnection, log_name: str, log_to_clone: GlobalWellLog,
                   well: Well, md: List[float], values: List[float], template: Optional[Any] = None):
    """
    Writes log data back to a well in Petrel.
    This is a robust function modeled after the working version in Vsh_calculator.py.
    """

    print(f"DEBUG: Starting write_back_log for {log_name} in well {well.petrel_name}")
    print(f"DEBUG: MD values count: {len(md)}, Log values count: {len(values)}")
    print(f"DEBUG: Log to clone: {log_to_clone.petrel_name}")
    print(f"DEBUG: Template: {template.petrel_name if template else 'None'}")

    try:
        # Helper to handle Petrel collections that may contain lists
        def get_object_list(collection):
            object_list = []
            for item in collection:
                if isinstance(item, list):
                    object_list.extend(item)
                else:
                    object_list.append(item)
            return object_list

        print("DEBUG: Determining global log collection type")
        # Determine which global log collection to search
        if isinstance(log_to_clone, DiscreteGlobalWellLog):
            gwl_collection = petrel.discrete_global_well_logs
            print("DEBUG: Using discrete global well logs collection")
        else:
            gwl_collection = petrel.global_well_logs
            print("DEBUG: Using continuous global well logs collection")

        print(f"DEBUG: Searching for existing global log: {log_name}")
        gwl = [i for i in get_object_list(gwl_collection) if i.petrel_name == log_name]

        # If global well log doesn't exist, create a new one.
        if not gwl:
            print(f'Creating new global well log: {log_name}')
            print("DEBUG: Cloning global log from template")
            global_log = log_to_clone.clone(name_of_clone=log_name, template=template)
            gwl = [global_log]
            print("DEBUG: New global log created successfully")
        else:
            print(f"DEBUG: Found existing global log: {log_name}")

        print("DEBUG: Checking for existing well log in target well")
        # Check if the well already has this log
        well_log = [i for i in get_object_list(well.logs) if i.petrel_name == log_name]

        # If well has the log, overwrite its values.
        if len(well_log) == 1:
            print("DEBUG: Well log exists, overwriting values")
            well_log[0].readonly = False
            well_log[0].set_values(md, values)
            print(f"Values for {log_name} overwritten for {well.petrel_name}")
            print("DEBUG: Well log values overwritten successfully")
        # If well does not have this log, create it and set its values.
        elif not well_log:
            print(f'Creating new well log: {log_name} for well {well.petrel_name}')
            print("DEBUG: Creating new well log from global log")
            new_log = gwl[0].create_well_log(well)
            new_log.readonly = False
            new_log.set_values(md, values)
            print(f"New well log {log_name} created for well {well.petrel_name}")
            print("DEBUG: New well log created and values set successfully")
        else:
            print(f"WARNING: Multiple well logs found with name {log_name} in well {well.petrel_name}")

        print(f"DEBUG: write_back_log completed successfully for {log_name}")

    except Exception as e:
        print(f"ERROR: Failed to write back log {log_name} for well {well.petrel_name}: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: MD values sample: {md[:5] if len(md) > 5 else md}")
        print(f"DEBUG: Log values sample: {values[:5] if len(values) > 5 else values}")
        raise


def create_petrel_output_logs_with_writeback(petrel: PetrelConnection, data: pd.DataFrame,
                                           target_log_name: str, well_ids: List[str], suffix: str,
                                           log_to_clone: GlobalWellLog, template: Optional[Any] = None):
    """
    Iterates through wells to create new logs in Petrel using the robust write_back_log function.
    """
    print("Creating output logs in Petrel...")
    output_log_name = target_log_name + suffix
    print(f"DEBUG: Output log name: {output_log_name}")
    print(f"DEBUG: Input data shape: {data.shape}")
    print(f"DEBUG: Well IDs count: {len(well_ids)}")

    try:
        print("DEBUG: Retrieving well objects from GUIDs")
        wells = [obj for obj in petrel.get_petrelobjects_by_guids(well_ids) if isinstance(obj, Well)]
        if not wells:
            print("ERROR: No valid wells found for writing back.")
            return

        print(f"DEBUG: Found {len(wells)} valid wells for write-back")
        print(f"DEBUG: Well names: {[well.petrel_name for well in wells]}")

        for well in wells:
            print(f"Processing well: {well.petrel_name}")
            print(f"DEBUG: Filtering data for well: {well.petrel_name}")

            well_data = data[data['WELL'] == well.petrel_name]
            if well_data.empty:
                print(f"WARNING: No data found for well {well.petrel_name}, skipping")
                continue

            print(f"  Well data samples: {len(well_data)}")
            print(f"DEBUG: Well data shape: {well_data.shape}")
            print(f"DEBUG: Well data columns: {list(well_data.columns)}")

            try:
                print("DEBUG: Extracting MD values")
                md_values = well_data['MD'].values
                print(f"DEBUG: MD values count: {len(md_values)}")

                # Use predicted values if they exist (from re-prediction mode), otherwise use the imputed values.
                print("DEBUG: Determining which log values to use")
                if f'{target_log_name}_predicted' in well_data.columns:
                    log_values = well_data[f'{target_log_name}_predicted'].values
                    print(f"DEBUG: Using predicted values column: {target_log_name}_predicted")
                else:
                    log_values = well_data[target_log_name].values
                    print(f"DEBUG: Using original/imputed values column: {target_log_name}")

                print(f"DEBUG: Log values count: {len(log_values)}")

                # IMPORTANT: Remove any NaN values to ensure data integrity before writing.
                print("DEBUG: Cleaning NaN values from data")
                valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)
                md_clean = md_values[valid_mask].tolist()
                log_clean = log_values[valid_mask].tolist()

                print(f"DEBUG: Valid data points after cleaning: {len(md_clean)}")
                print(f"DEBUG: Removed {len(md_values) - len(md_clean)} NaN values")

                if not md_clean:
                    print(f"WARNING: No valid data points to write for well {well.petrel_name}")
                    continue

                # Call the robust, refactored write-back function
                print(f"DEBUG: Calling write_back_log for well {well.petrel_name}")
                write_back_log(
                    petrel=petrel,
                    log_name=output_log_name,
                    log_to_clone=log_to_clone,
                    well=well,
                    md=md_clean,
                    values=log_clean,
                    template=template
                )
                print(f"DEBUG: write_back_log completed for well {well.petrel_name}")

            except Exception as e:
                import traceback
                print(f"ERROR: Error processing well log for {well.petrel_name}: {e}")
                print(f"DEBUG: Exception type: {type(e)}")
                print(f"DEBUG: Traceback: {traceback.format_exc()}")

        print(f"✓ Completed output log creation.")
        print("DEBUG: create_petrel_output_logs_with_writeback completed successfully")

    except Exception as e:
        print(f"ERROR: Failed in create_petrel_output_logs_with_writeback: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Data type: {type(data)}")
        print(f"DEBUG: Well IDs type: {type(well_ids)}")
        raise


# Execute main function directly (following Vsh_calculator.py pattern)
main()
