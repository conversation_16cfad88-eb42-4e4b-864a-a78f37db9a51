#!/usr/bin/env python3
"""
Test script for Missing Log Imputation ML framework
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime

# Test basic imports
print("Testing basic imports...")
try:
    import numpy as np
    import pandas as pd
    import matplotlib.pyplot as plt
    print("✓ Core imports successful")
except ImportError as e:
    print(f"✗ Core import error: {e}")
    sys.exit(1)

try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.linear_model import LinearRegression
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_absolute_error, r2_score
    print("✓ Scikit-learn imports successful")
except ImportError as e:
    print(f"✗ Scikit-learn import error: {e}")
    print("Please install scikit-learn: pip install scikit-learn")
    sys.exit(1)

# Test optional imports
print("\nTesting optional ML libraries...")
try:
    from xgboost import XGBRegressor
    print("✓ XGBoost available")
    XGBOOST_AVAILABLE = True
except ImportError:
    print("✗ XGBoost not available")
    XGBOOST_AVAILABLE = False

try:
    from lightgbm import LGBMRegressor
    print("✓ LightGBM available")
    LIGHTGBM_AVAILABLE = True
except ImportError:
    print("✗ LightGBM not available")
    LIGHTGBM_AVAILABLE = False

try:
    from catboost import CatBoostRegressor
    print("✓ CatBoost available")
    CATBOOST_AVAILABLE = True
except ImportError:
    print("✗ CatBoost not available")
    CATBOOST_AVAILABLE = False

# Test synthetic data generation
print("\nTesting synthetic data generation...")
try:
    np.random.seed(42)
    
    # Generate simple synthetic well log data
    n_samples = 1000
    md = np.linspace(1000, 2000, n_samples)
    
    # Generate correlated logs
    depth_trend = (md - md.min()) / (md.max() - md.min())
    
    # GR (Gamma Ray)
    gr = 50 + 100 * depth_trend + np.random.normal(0, 15, n_samples)
    gr = np.clip(gr, 0, 300)
    
    # RHOB (Bulk Density)
    rhob = 2.0 + 0.5 * depth_trend + np.random.normal(0, 0.1, n_samples)
    rhob = np.clip(rhob, 1.5, 3.0)
    
    # NPHI (Neutron Porosity)
    nphi = 0.4 - 0.3 * depth_trend + np.random.normal(0, 0.05, n_samples)
    nphi = np.clip(nphi, 0, 1)
    
    # Vp (P-wave velocity)
    vp = 2000 + 2000 * rhob/3.0 - 1500 * nphi + np.random.normal(0, 200, n_samples)
    vp = np.clip(vp, 1500, 6000)
    
    # Vs (S-wave velocity)
    vs = vp * (0.5 + 0.2 * (rhob - 2.0)) + np.random.normal(0, 100, n_samples)
    vs = np.clip(vs, 800, 3500)
    
    # Create DataFrame
    data = pd.DataFrame({
        'WELL': 'TEST_WELL',
        'MD': md,
        'GR': gr,
        'RHOB': rhob,
        'NPHI': nphi,
        'Vp': vp,
        'Vs': vs
    })
    
    print(f"✓ Generated {len(data)} synthetic log samples")
    print(f"  Data shape: {data.shape}")
    print(f"  Columns: {list(data.columns)}")
    
except Exception as e:
    print(f"✗ Error generating synthetic data: {e}")
    sys.exit(1)

# Test missing value introduction
print("\nTesting missing value introduction...")
try:
    data_with_missing = data.copy()
    
    # Introduce 20% missing values in Vs
    missing_mask = np.random.random(len(data_with_missing)) < 0.2
    data_with_missing.loc[missing_mask, 'Vs'] = np.nan
    
    missing_count = data_with_missing['Vs'].isna().sum()
    missing_percentage = (missing_count / len(data_with_missing)) * 100
    
    print(f"✓ Introduced {missing_count} missing values ({missing_percentage:.1f}%)")
    
except Exception as e:
    print(f"✗ Error introducing missing values: {e}")
    sys.exit(1)

# Test ML model training
print("\nTesting ML model training...")
try:
    # Prepare data for training
    target_col = 'Vs'
    feature_cols = ['MD', 'GR', 'RHOB', 'NPHI', 'Vp']
    
    # Get complete cases
    complete_mask = data_with_missing[target_col].notna()
    train_data = data_with_missing[complete_mask].copy()
    
    X = train_data[feature_cols].fillna(train_data[feature_cols].mean())
    y = train_data[target_col]
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)
    
    print(f"  Training samples: {len(X_train)}")
    print(f"  Test samples: {len(X_test)}")
    
    # Test different models
    models = {
        'Linear_Regression': LinearRegression(),
        'Random_Forest': RandomForestRegressor(n_estimators=50, random_state=42)
    }
    
    if XGBOOST_AVAILABLE:
        models['XGBoost'] = XGBRegressor(n_estimators=50, random_state=42)
    
    results = {}
    for model_name, model in models.items():
        print(f"  Training {model_name}...")
        
        # Train model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        results[model_name] = {'mae': mae, 'r2': r2}
        print(f"    MAE: {mae:.3f}, R²: {r2:.3f}")
    
    print("✓ ML model training successful")
    
except Exception as e:
    print(f"✗ Error in ML model training: {e}")
    sys.exit(1)

# Test imputation
print("\nTesting imputation...")
try:
    # Use the best model (lowest MAE)
    best_model_name = min(results.keys(), key=lambda k: results[k]['mae'])
    best_model = models[best_model_name]
    
    print(f"  Using best model: {best_model_name}")
    
    # Impute missing values
    missing_mask = data_with_missing[target_col].isna()
    X_missing = data_with_missing.loc[missing_mask, feature_cols].fillna(
        data_with_missing[feature_cols].mean()
    )
    
    predictions = best_model.predict(X_missing)
    
    # Fill missing values
    data_imputed = data_with_missing.copy()
    data_imputed.loc[missing_mask, target_col] = predictions
    
    imputed_count = missing_mask.sum()
    remaining_missing = data_imputed[target_col].isna().sum()
    
    print(f"  Imputed {imputed_count} missing values")
    print(f"  Remaining missing values: {remaining_missing}")
    
    # Calculate accuracy on known values
    original_values = data.loc[missing_mask, target_col]
    imputed_values = data_imputed.loc[missing_mask, target_col]
    
    imputation_mae = mean_absolute_error(original_values, imputed_values)
    imputation_r2 = r2_score(original_values, imputed_values)
    
    print(f"  Imputation accuracy - MAE: {imputation_mae:.3f}, R²: {imputation_r2:.3f}")
    print("✓ Imputation successful")
    
except Exception as e:
    print(f"✗ Error in imputation: {e}")
    sys.exit(1)

# Test visualization (basic)
print("\nTesting basic visualization...")
try:
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    import matplotlib.pyplot as plt
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))
    
    # Plot 1: Model comparison
    model_names = list(results.keys())
    mae_values = [results[name]['mae'] for name in model_names]
    
    axes[0].bar(model_names, mae_values)
    axes[0].set_title('Model Comparison (MAE)')
    axes[0].set_ylabel('Mean Absolute Error')
    axes[0].tick_params(axis='x', rotation=45)
    
    # Plot 2: Original vs Imputed
    axes[1].scatter(original_values, imputed_values, alpha=0.6)
    axes[1].plot([original_values.min(), original_values.max()], 
                [original_values.min(), original_values.max()], 'r--', lw=2)
    axes[1].set_xlabel('Original Values')
    axes[1].set_ylabel('Imputed Values')
    axes[1].set_title('Original vs Imputed Values')
    
    plt.tight_layout()
    
    # Save plot
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plot_filename = f'test_results_{timestamp}.png'
    plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Basic visualization successful - saved as {plot_filename}")
    
except Exception as e:
    print(f"✗ Error in visualization: {e}")

print("\n" + "="*60)
print("ALL TESTS COMPLETED SUCCESSFULLY!")
print("="*60)
print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("\nThe Missing Log Imputation ML framework is ready for use.")
