# Missing Log Imputation - Petrel Interactive

## Overview

This enhanced version of the missing log imputation script provides seamless integration with Petrel, allowing users to select wells and logs directly from the current Petrel project through an intuitive interface. The script leverages the `pythontool` library to access Petrel data and create output logs directly in the project.

## Key Features

### 🎯 **Direct Petrel Integration**
- **Well Selection**: Choose index well and input wells directly from Petrel project
- **Log Selection**: Select input log curves and target log from available global well logs
- **Real-time Data Access**: Load data directly from Petrel without external files
- **Output Integration**: Create new well logs in Petrel with imputed values

### 🔧 **Interactive Configuration**
- **Index Well**: Select a reference well with complete log data for training
- **Input Wells**: Choose multiple wells including those with missing data
- **Input Logs**: Select log curves to use as features (GR, RHOB, NPHI, Vp, etc.)
- **Target Log**: Choose the log curve that needs imputation

### 📊 **Multiple Imputation Modes**
1. **Missing Values Only**: Impute only the missing values in the target log
2. **Cross-Validation Test**: Evaluate model performance without creating new logs
3. **Full Re-prediction**: Re-predict all values including existing ones

### 🤖 **Advanced ML Algorithms**
- **Basic Models**: Linear, Ridge, Lasso Regression
- **Ensemble Methods**: Random Forest, Extra Trees, Gradient Boosting
- **Advanced Boosting**: XGBoost, LightGBM, CatBoost (optional)
- **Automatic Model Selection**: Best model chosen based on validation performance

## User Interface Parameters

### **Data Selection**
| Parameter | Type | Description |
|-----------|------|-------------|
| `Index Well (Reference)` | Well Object | Reference well with complete log data for training |
| `Input Wells` | Multiple Wells | Wells to include in analysis (with/without missing data) |
| `Input Log Names` | String | Comma-separated log names (e.g., "GR,RHOB,NPHI,Vp") |
| `Target Log Name` | String | Name of the log curve that needs imputation (e.g., "Vs") |

### **Processing Options**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `Imputation Mode` | Enum | Missing Only/Cross-Validation/Full Re-prediction | Missing Only |
| `Test Data Percentage` | Float | Percentage for testing (10-50%) | 25.0 |
| `Include Depth as Feature` | Boolean | Use MD as additional feature | True |
| `Enable Advanced Models` | Boolean | Include XGBoost, LightGBM, CatBoost | True |
| `Use Cross Validation` | Boolean | Perform k-fold cross validation | True |
| `CV Folds` | Integer | Number of cross-validation folds (3-10) | 5 |

### **Output Options**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `Create Output Logs` | Boolean | Create new well logs in Petrel | True |
| `Output Log Suffix` | String | Suffix for output log names | "_ML_imputed" |
| `Generate Plots` | Boolean | Create visualization plots | True |

## Workflow Steps

### 1. **Data Loading and Validation**
```
✓ Connect to Petrel project
✓ Load selected wells and logs
✓ Validate data availability and coverage
✓ Identify index well characteristics
```

### 2. **Data Analysis**
```
✓ Calculate coverage statistics for each log
✓ Analyze missing data patterns by well
✓ Validate index well completeness
✓ Prepare feature matrix
```

### 3. **Model Training and Evaluation**
```
✓ Train multiple ML algorithms
✓ Perform cross-validation
✓ Calculate performance metrics (MAE, RMSE, R²)
✓ Select best performing model
```

### 4. **Imputation and Output**
```
✓ Apply selected imputation mode
✓ Create new well logs in Petrel (if enabled)
✓ Generate analysis visualizations
✓ Export performance summary
```

## Output Files and Logs

### **Petrel Well Logs**
- **Imputed Logs**: New well logs with suffix (e.g., "Vs_ML_imputed")
- **Predicted Logs**: Full re-prediction logs (e.g., "Vs_ML_imputed_predicted")
- **Global Log Creation**: Automatically creates global well logs for consistency

### **Analysis Plots**
- **Model Performance**: Bar chart comparing algorithm performance
- **Data Coverage**: Heatmap showing coverage by well and log type
- **Target Distribution**: Histogram of target log values
- **Missing Patterns**: Bar chart of missing values by well

### **Console Output**
- Real-time progress updates
- Data loading statistics
- Model training results
- Performance metrics summary
- Error handling and warnings

## Technical Implementation

### **Data Access Pattern**
```python
# Connect to Petrel
petrel = PetrelConnection(allow_experimental=True)

# Get selected objects
wells = petrel.get_petrelobjects_by_guids(well_ids)
logs = petrel.get_petrelobjects_by_guids(log_ids)

# Load data using logs_dataframe
well_df = well.logs_dataframe(selected_logs)
```

### **Model Training Framework**
```python
# Prepare data with missing value handling
imputer = SimpleImputer(strategy='mean')
X_imputed = imputer.fit_transform(features)

# Train multiple algorithms
models = {
    'Linear_Regression': LinearRegression(),
    'Random_Forest': RandomForestRegressor(),
    'XGBoost': XGBRegressor(),  # if available
    # ... more models
}

# Cross-validation and selection
best_model = select_best_by_cv_score(models, X, y)
```

### **Petrel Output Creation**
```python
# Clone existing global well log
new_global_log = target_log.clone(output_name, copy_values=False)

# Create well logs for each well
for well in wells:
    well_log = new_global_log.create_well_log(well)
    well_log.set_values(md_values, imputed_values)
```

## Best Practices

### **Well Selection Strategy**
1. **Index Well**: Choose well with >80% target log coverage
2. **Input Wells**: Include diverse geological settings
3. **Minimum Wells**: Use at least 3-5 wells for robust training
4. **Data Quality**: Verify log quality and consistency

### **Feature Selection**
1. **Correlated Logs**: Include logs physically related to target
2. **Depth Feature**: Usually beneficial for geological trends
3. **Avoid Redundancy**: Don't include highly correlated features
4. **Quality Check**: Ensure input logs have reasonable coverage

### **Model Selection Guidelines**
1. **Start Simple**: Begin with Linear/Random Forest models
2. **Advanced Models**: Use for complex geological relationships
3. **Cross-Validation**: Always validate model performance
4. **Geological Sense**: Verify predictions are geologically reasonable

### **Quality Control**
1. **Coverage Analysis**: Review data coverage before training
2. **Performance Metrics**: Check R² > 0.7 for good predictions
3. **Visual Inspection**: Plot original vs predicted values
4. **Geological Review**: Validate results with domain experts

## Troubleshooting

### **Common Issues**

#### **Connection Problems**
```
Error: Cannot connect to Petrel
Solution: Ensure Petrel is running and Python Tool Pro is active
```

#### **Data Loading Issues**
```
Error: No data found for selected wells/logs
Solution: Verify wells contain the selected global well logs
```

#### **Insufficient Data**
```
Error: Insufficient complete data for training
Solution: Select wells with better target log coverage or different target log
```

#### **Model Training Failures**
```
Error: Model training failed
Solution: Check for sufficient data, disable advanced models if packages missing
```

### **Performance Optimization**
1. **Large Datasets**: Disable cross-validation for faster processing
2. **Memory Issues**: Reduce number of wells or use simpler models
3. **Speed**: Disable advanced models if not needed
4. **Accuracy**: Increase CV folds for better model selection

## Integration Examples

### **Typical Workflow**
1. Open Petrel project with well log data
2. Launch Python Tool Pro
3. Load "Missing Log Imputation - Petrel Interactive" script
4. Configure parameters through UI:
   - Select index well with good Vs coverage
   - Choose input wells including those missing Vs
   - Select input logs: GR, RHOB, NPHI, Vp
   - Set target log: Vs
5. Run workflow
6. Review results and new imputed logs in Petrel

### **Quality Assurance Workflow**
1. Run in "Cross-Validation Test" mode first
2. Review model performance metrics
3. If satisfactory (R² > 0.7), run "Missing Values Only" mode
4. Create output logs and review in log plots
5. Compare with geological expectations
6. Document methodology and results

---

**Integration**: Seamless Petrel workflow  
**Performance**: Production-ready with error handling  
**Flexibility**: Multiple modes and configuration options  
**Quality**: Built-in validation and quality control  

This interactive version transforms the standalone script into a fully integrated Petrel workflow tool, making missing log imputation accessible to geoscientists working directly within their familiar Petrel environment.
