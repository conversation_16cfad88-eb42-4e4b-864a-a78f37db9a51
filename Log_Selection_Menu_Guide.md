# Log Selection Menu Implementation Guide

## Overview
This guide explains how to implement dropdown menus for selecting input and target logs in Petrel workflows using the Cegal Prizm Python Tool framework.

## Key Changes Made

### 1. Modified Missing_Log_Imputation_Petrel_Interactive.py

**Before (String Input):**
```python
pwr_description.add_string_parameter(name='input_log_names', label='Input Log Names', description='Enter log names separated by commas (e.g., GR,RHOB,NPHI,Vp)', default_value='GR,RHOB,NPHI,Vp')
pwr_description.add_string_parameter(name='target_log_name', label='Target Log Name', description='Enter the name of the log to impute (e.g., Vs)', default_value='Vs')
```

**After (Dropdown Menus):**
```python
# Input logs selection - multiple selection dropdown
pwr_description.add_object_ref_parameter(name='input_log_ids', 
                                        label='Input Logs', 
                                        description='Select input logs for features (e.g., GR, RHOB, NPHI, Vp)', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        select_multiple=True, 
                                        linked_input_name='index_well_id')

# Target log selection - single selection dropdown  
pwr_description.add_object_ref_parameter(name='target_log_id', 
                                        label='Target Log', 
                                        description='Select the target log to impute (e.g., Vs)', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        linked_input_name='index_well_id')
```

### 2. Updated Parameter Processing

**Before:**
```python
input_log_names_str = parameters['input_log_names']
target_log_name = parameters['target_log_name']
input_log_names = [name.strip() for name in input_log_names_str.split(',') if name.strip()]
```

**After:**
```python
input_log_ids = parameters['input_log_ids']
target_log_id = parameters['target_log_id']

# Get log names from selected log objects
input_log_objects = petrel.get_petrelobjects_by_guids(input_log_ids)
input_log_names = [log.petrel_name for log in input_log_objects if hasattr(log, 'petrel_name')]

target_log_object = petrel.get_petrelobjects_by_guids([target_log_id])[0]
target_log_name = target_log_object.petrel_name
```

## Key Parameters for Dropdown Menus

### 1. Basic Log Selection
```python
pwr_description.add_object_ref_parameter(
    name='log_id',                                    # Parameter name
    label='Select Log',                               # UI label
    description='Select a well log',                  # Tooltip description
    object_type=DomainObjectsEnum.WellContinuousLog  # Object type
)
```

### 2. Multiple Log Selection
```python
pwr_description.add_object_ref_parameter(
    name='log_ids',
    label='Select Multiple Logs',
    description='Select multiple well logs',
    object_type=DomainObjectsEnum.WellContinuousLog,
    select_multiple=True                              # Enable multiple selection
)
```

### 3. Linked to Well Selection
```python
pwr_description.add_object_ref_parameter(
    name='well_id',
    label='Select Well',
    description='Select a well',
    object_type=DomainObjectsEnum.Well
)

pwr_description.add_object_ref_parameter(
    name='log_id',
    label='Select Log',
    description='Select a log from the chosen well',
    object_type=DomainObjectsEnum.WellContinuousLog,
    linked_input_name='well_id'                      # Link to well selection
)
```

### 4. Template-Based Selection
```python
pwr_description.add_object_ref_parameter(
    name='gamma_ray_log_id',
    label='Gamma Ray Log',
    description='Select a gamma ray log',
    object_type=DomainObjectsEnum.WellContinuousLog,
    template_type='GammaRay',                        # Filter by template type
    linked_input_name='well_id'
)
```

## Available Object Types for Logs

- `DomainObjectsEnum.WellContinuousLog` - Continuous well logs
- `DomainObjectsEnum.WellDiscreteLog` - Discrete well logs
- `DomainObjectsEnum.GlobalWellLog` - Global well logs

## Benefits of Dropdown Menus

1. **User-Friendly**: No need to type log names manually
2. **Error Prevention**: Eliminates typos and invalid log names
3. **Dynamic**: Shows only available logs in the project
4. **Validation**: Ensures selected logs exist
5. **Linked Selection**: Can filter logs based on selected well
6. **Template Filtering**: Can filter logs by specific types (e.g., GammaRay)

## Example Usage in Petrel

1. User selects a well from the "Index Well" dropdown
2. The "Input Logs" dropdown automatically populates with logs available in that well
3. User can select multiple input logs (e.g., GR, RHOB, NPHI)
4. The "Target Log" dropdown shows available target logs
5. User selects a single target log (e.g., Vs)

## Demo Script

The `Log_Selection_Demo.py` script demonstrates:
- Basic well selection
- Multiple input log selection
- Single target log selection
- Template-based log selection (Gamma Ray)
- Data loading and validation

## Integration with Existing Code

The modified `Missing_Log_Imputation_Petrel_Interactive.py` maintains all existing functionality while adding the dropdown menu interface. The rest of the ML pipeline remains unchanged.

## Best Practices

1. Always use `linked_input_name` to connect log selection to well selection
2. Add validation to ensure required logs are selected
3. Use descriptive labels and descriptions for better user experience
4. Consider using `template_type` for specific log types when appropriate
5. Handle exceptions when retrieving log objects by GUID
