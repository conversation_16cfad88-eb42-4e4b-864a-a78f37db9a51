"""
Example script demonstrating how to use the Petrel Interactive Missing Log Imputation

This script shows how to programmatically access Petrel wells and logs,
and demonstrates the key concepts used in the interactive imputation workflow.

Note: This is for demonstration purposes. The actual interactive script
uses the PWR interface for user selection.
"""

from cegalprizm.pythontool import PetrelConnection
import pandas as pd
import numpy as np

def demonstrate_petrel_data_access():
    """Demonstrate how to access wells and logs from Petrel"""
    
    print("="*60)
    print("PETREL DATA ACCESS DEMONSTRATION")
    print("="*60)
    
    try:
        # Connect to Petrel
        petrel = PetrelConnection(allow_experimental=True)
        print(f"✓ Connected to Petrel project: {petrel.get_current_project_name()}")
        print()
        
        # List all wells in the project
        print("Available wells in project:")
        wells = list(petrel.wells)
        for i, well in enumerate(wells[:10]):  # Show first 10 wells
            print(f"  {i+1}. {well.petrel_name} (GUID: {well.guid})")
        
        if len(wells) > 10:
            print(f"  ... and {len(wells) - 10} more wells")
        print(f"Total wells: {len(wells)}")
        print()
        
        # List all global well logs
        print("Available global well logs:")
        global_logs = list(petrel.global_well_logs)
        for i, log in enumerate(global_logs[:15]):  # Show first 15 logs
            print(f"  {i+1}. {log.petrel_name} (GUID: {log.guid})")
        
        if len(global_logs) > 15:
            print(f"  ... and {len(global_logs) - 15} more logs")
        print(f"Total global well logs: {len(global_logs)}")
        print()
        
        # Demonstrate data loading from a well (if wells exist)
        if wells:
            example_well = wells[0]
            print(f"Demonstrating data access from well: {example_well.petrel_name}")
            
            # Get logs available for this well
            well_logs = list(example_well.logs)
            print(f"  Available logs in this well: {len(well_logs)}")
            for log in well_logs[:5]:  # Show first 5 logs
                print(f"    - {log.petrel_name}")
            
            # Try to load data using common log names
            common_logs = ['GR', 'RHOB', 'NPHI', 'Vp', 'Vs', 'DT', 'CALI']
            available_global_logs = []
            
            for log_name in common_logs:
                matching_logs = [log for log in global_logs if log.petrel_name == log_name]
                if matching_logs:
                    available_global_logs.append(matching_logs[0])
                    print(f"  ✓ Found global log: {log_name}")
            
            if available_global_logs:
                print(f"\n  Loading data using {len(available_global_logs)} global logs...")
                
                try:
                    # Load data as DataFrame
                    df = example_well.logs_dataframe(available_global_logs)
                    
                    if not df.empty:
                        print(f"  ✓ Successfully loaded data: {df.shape[0]} samples, {df.shape[1]} columns")
                        print(f"  Columns: {list(df.columns)}")
                        
                        # Show data coverage
                        print("\n  Data coverage:")
                        for col in df.columns:
                            coverage = (1 - df[col].isna().mean()) * 100
                            print(f"    {col}: {coverage:.1f}%")
                        
                        # Show sample data
                        print(f"\n  Sample data (first 5 rows):")
                        print(df.head().to_string())
                        
                    else:
                        print("  ⚠ No data loaded (empty DataFrame)")
                        
                except Exception as e:
                    print(f"  ✗ Error loading data: {str(e)}")
            
            else:
                print("  ⚠ No common log types found in this project")
        
        else:
            print("⚠ No wells found in the project")
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED")
        print("="*60)
        
    except Exception as e:
        print(f"✗ Error connecting to Petrel: {str(e)}")
        print("Make sure Petrel is running and Python Tool Pro is properly configured.")


def demonstrate_log_selection_logic():
    """Demonstrate the logic for selecting appropriate logs for imputation"""
    
    print("\n" + "="*60)
    print("LOG SELECTION LOGIC DEMONSTRATION")
    print("="*60)
    
    # Typical log relationships for imputation
    log_relationships = {
        'Vs': {
            'primary_features': ['Vp', 'RHOB', 'NPHI'],
            'secondary_features': ['GR', 'DT'],
            'description': 'S-wave velocity prediction from P-wave, density, and porosity'
        },
        'Vp': {
            'primary_features': ['RHOB', 'NPHI', 'DT'],
            'secondary_features': ['GR', 'Vs'],
            'description': 'P-wave velocity prediction from density, porosity, and transit time'
        },
        'RHOB': {
            'primary_features': ['NPHI', 'GR', 'Vp'],
            'secondary_features': ['DT', 'Vs'],
            'description': 'Bulk density prediction from porosity, gamma ray, and velocity'
        },
        'NPHI': {
            'primary_features': ['RHOB', 'GR', 'DT'],
            'secondary_features': ['Vp', 'Vs'],
            'description': 'Neutron porosity prediction from density, gamma ray, and transit time'
        },
        'GR': {
            'primary_features': ['NPHI', 'RHOB'],
            'secondary_features': ['Vp', 'Vs', 'DT'],
            'description': 'Gamma ray prediction from porosity and density logs'
        }
    }
    
    print("Recommended feature selection for common target logs:")
    print()
    
    for target_log, info in log_relationships.items():
        print(f"Target Log: {target_log}")
        print(f"  Description: {info['description']}")
        print(f"  Primary features: {', '.join(info['primary_features'])}")
        print(f"  Secondary features: {', '.join(info['secondary_features'])}")
        print()
    
    print("Selection Guidelines:")
    print("1. Index Well: Choose well with >80% coverage of target log")
    print("2. Input Wells: Include 3-5 wells with diverse geological settings")
    print("3. Features: Select logs physically related to target log")
    print("4. Depth: Include MD (measured depth) for geological trends")
    print("5. Quality: Ensure input logs have >50% coverage across wells")


def demonstrate_imputation_modes():
    """Demonstrate the different imputation modes available"""
    
    print("\n" + "="*60)
    print("IMPUTATION MODES DEMONSTRATION")
    print("="*60)
    
    modes = {
        0: {
            'name': 'Missing Values Only',
            'description': 'Impute only the missing values in the target log',
            'use_case': 'Standard imputation workflow for production use',
            'output': 'New well logs with missing values filled',
            'validation': 'Cross-validation on complete data'
        },
        1: {
            'name': 'Cross-Validation Test',
            'description': 'Evaluate model performance without creating new logs',
            'use_case': 'Quality assessment and model validation',
            'output': 'Performance metrics and plots only',
            'validation': 'Hold-out test on artificially missing data'
        },
        2: {
            'name': 'Full Re-prediction',
            'description': 'Re-predict all values including existing ones',
            'use_case': 'Quality control and data consistency checking',
            'output': 'New logs with all predicted values',
            'validation': 'Comparison between original and predicted values'
        }
    }
    
    for mode_id, info in modes.items():
        print(f"Mode {mode_id}: {info['name']}")
        print(f"  Description: {info['description']}")
        print(f"  Use Case: {info['use_case']}")
        print(f"  Output: {info['output']}")
        print(f"  Validation: {info['validation']}")
        print()
    
    print("Recommended Workflow:")
    print("1. Start with Mode 1 (Cross-Validation Test) to assess model quality")
    print("2. If R² > 0.7 and MAE is acceptable, proceed with Mode 0 (Missing Values Only)")
    print("3. Use Mode 2 (Full Re-prediction) for quality control and comparison")


def demonstrate_quality_control():
    """Demonstrate quality control checks for imputation results"""
    
    print("\n" + "="*60)
    print("QUALITY CONTROL DEMONSTRATION")
    print("="*60)
    
    print("Key Quality Metrics:")
    print()
    
    quality_checks = {
        'Statistical Metrics': {
            'MAE (Mean Absolute Error)': 'Lower is better, should be <10% of log range',
            'RMSE (Root Mean Square Error)': 'Lower is better, penalizes large errors',
            'R² (Coefficient of Determination)': 'Higher is better, >0.7 indicates good fit',
            'CV Score': 'Cross-validation consistency, low std deviation preferred'
        },
        'Data Quality Checks': {
            'Coverage Analysis': 'Ensure >50% coverage for input features',
            'Missing Pattern': 'Check if missing data is random or systematic',
            'Outlier Detection': 'Identify and handle extreme values',
            'Geological Consistency': 'Verify results match expected trends'
        },
        'Model Validation': {
            'Cross-Validation': 'K-fold validation for robust performance estimate',
            'Hold-out Testing': 'Independent test set for unbiased evaluation',
            'Feature Importance': 'Check which features contribute most to predictions',
            'Residual Analysis': 'Examine prediction errors for patterns'
        }
    }
    
    for category, checks in quality_checks.items():
        print(f"{category}:")
        for check, description in checks.items():
            print(f"  • {check}: {description}")
        print()
    
    print("Quality Control Workflow:")
    print("1. Pre-processing: Check data coverage and quality")
    print("2. Model Training: Use cross-validation for robust evaluation")
    print("3. Model Selection: Choose best performing algorithm")
    print("4. Result Validation: Compare predictions with geological expectations")
    print("5. Documentation: Record methodology and performance metrics")


if __name__ == "__main__":
    """
    Run demonstrations of Petrel integration concepts
    
    Note: This requires an active Petrel connection with well log data
    """
    
    print("PETREL INTEGRATION DEMONSTRATION SUITE")
    print("This script demonstrates key concepts used in the interactive imputation workflow")
    print()
    
    try:
        # Demonstrate Petrel data access
        demonstrate_petrel_data_access()
        
        # Demonstrate log selection logic
        demonstrate_log_selection_logic()
        
        # Demonstrate imputation modes
        demonstrate_imputation_modes()
        
        # Demonstrate quality control
        demonstrate_quality_control()
        
        print("\n" + "="*60)
        print("ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY")
        print("="*60)
        print()
        print("Next Steps:")
        print("1. Use 'Missing_Log_Imputation_Petrel_Interactive.py' for actual imputation")
        print("2. Configure parameters through the PWR interface")
        print("3. Select appropriate wells and logs based on demonstrations above")
        print("4. Follow quality control guidelines for best results")
        
    except Exception as e:
        print(f"Error during demonstration: {str(e)}")
        print("Make sure Petrel is running with well log data loaded")
