# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="Missing Log Imputation with Multiple ML Algorithms",
                                      category="Well log analysis",
                                      description="Comprehensive workflow for missing well log imputation using multiple machine learning algorithms including XGBoost, LightGBM, CatBoost, Random Forest, and TPOT AutoML framework. Generates synthetic well log data and compares imputation performance.",
                                      authors="BKP_Team@PTM",
                                      version="1.0")

# --- Define the UI for the Prizm Workflow Runner ---

pwr_description.add_integer_parameter(name='n_wells', label='Number of Wells', description='Number of synthetic wells to generate', default_value=20, minimum_value=5, maximum_value=100)
pwr_description.add_integer_parameter(name='samples_per_well', label='Samples per Well', description='Number of log samples per well', default_value=500, minimum_value=100, maximum_value=2000)
pwr_description.add_float_parameter(name='missing_percentage', label='Missing Data Percentage', description='Percentage of data to artificially remove for testing imputation (0-50%)', default_value=20.0, minimum_value=0.0, maximum_value=50.0)
pwr_description.add_enum_parameter(name='target_log', label='Target Log for Imputation', description='Primary log to impute', options={0:'Vs', 1:'Vp', 2:'RHOB', 3:'NPHI', 4:'GR'}, default_value=0)
pwr_description.add_boolean_parameter(name='use_automl', label='Enable AutoML', description='Use TPOT AutoML framework for automated model selection', default_value=True)
pwr_description.add_boolean_parameter(name='generate_plots', label='Generate Visualizations', description='Create plots for data analysis and results comparison', default_value=True)
pwr_description.add_integer_parameter(name='random_seed', label='Random Seed', description='Random seed for reproducible results', default_value=42, minimum_value=1, maximum_value=9999)
pwr_description.add_float_parameter(name='test_size', label='Test Set Size', description='Fraction of data to use for testing (0.1-0.5)', default_value=0.25, minimum_value=0.1, maximum_value=0.5)
pwr_description.add_boolean_parameter(name='cross_validation', label='Use Cross Validation', description='Perform k-fold cross validation for model evaluation', default_value=True)
pwr_description.add_integer_parameter(name='cv_folds', label='CV Folds', description='Number of cross-validation folds', default_value=5, minimum_value=3, maximum_value=10)

# End: PWR Description

# Ensure the parameters dictionary is available for debugging
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    print("DEBUG: Using default parameters for testing")
    print(f"DEBUG: Default parameters: {parameters}")
else:
    print(f"DEBUG: Using provided parameters: {parameters}")

# Core libraries
import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import time
import os
from datetime import datetime
warnings.filterwarnings('ignore')

# Try to import seaborn, use matplotlib if not available
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    print("Seaborn not available. Using matplotlib for plotting.")

# Machine learning libraries
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer

# Advanced ML libraries
try:
    from xgboost import XGBRegressor
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost not available. Install with: pip install xgboost")

try:
    from lightgbm import LGBMRegressor
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM not available. Install with: pip install lightgbm")

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("CatBoost not available. Install with: pip install catboost")

# AutoML libraries
try:
    from tpot import TPOTRegressor
    TPOT_AVAILABLE = True
except ImportError:
    TPOT_AVAILABLE = False
    print("TPOT not available. Install with: pip install tpot")

# Petrel connection (if available)
try:
    from cegalprizm.pythontool import PetrelConnection
    PETREL_AVAILABLE = True
except ImportError:
    PETREL_AVAILABLE = False
    print("Petrel connection not available. Running in standalone mode.")


class SyntheticWellLogGenerator:
    """Generate realistic synthetic well log data with correlations and missing values"""
    
    def __init__(self, random_seed: int = 42):
        np.random.seed(random_seed)
        self.random_seed = random_seed
        
    def generate_well_logs(self, n_wells: int = 20, samples_per_well: int = 500) -> pd.DataFrame:
        """Generate synthetic well log data with realistic correlations"""
        
        all_data = []
        
        for well_idx in range(n_wells):
            well_name = f"WELL_{well_idx+1:03d}"
            
            # Generate measured depth
            md_start = np.random.uniform(1000, 1500)
            md_end = md_start + samples_per_well * np.random.uniform(0.1, 0.5)
            md = np.linspace(md_start, md_end, samples_per_well)
            
            # Generate base trends
            depth_trend = (md - md.min()) / (md.max() - md.min())
            
            # Generate GR (Gamma Ray) - typically 0-300 API
            gr_base = 50 + 100 * depth_trend + 30 * np.sin(depth_trend * 10)
            gr_noise = np.random.normal(0, 15, samples_per_well)
            gr = np.clip(gr_base + gr_noise, 0, 300)
            
            # Generate RHOB (Bulk Density) - typically 1.5-3.0 g/cc
            rhob_base = 2.0 + 0.5 * depth_trend + 0.2 * np.sin(depth_trend * 8)
            rhob_noise = np.random.normal(0, 0.1, samples_per_well)
            rhob = np.clip(rhob_base + rhob_noise, 1.5, 3.0)
            
            # Generate NPHI (Neutron Porosity) - typically 0-1 (fraction)
            nphi_base = 0.4 - 0.3 * depth_trend + 0.1 * np.sin(depth_trend * 12)
            nphi_noise = np.random.normal(0, 0.05, samples_per_well)
            nphi = np.clip(nphi_base + nphi_noise, 0, 1)
            
            # Generate Vp (P-wave velocity) - correlated with density and porosity
            vp_base = 2000 + 2000 * rhob/3.0 - 1500 * nphi + 200 * depth_trend
            vp_noise = np.random.normal(0, 200, samples_per_well)
            vp = np.clip(vp_base + vp_noise, 1500, 6000)
            
            # Generate Vs (S-wave velocity) - correlated with Vp
            vs_base = vp * (0.5 + 0.2 * (rhob - 2.0))
            vs_noise = np.random.normal(0, 100, samples_per_well)
            vs = np.clip(vs_base + vs_noise, 800, 3500)
            
            # Create well dataframe
            well_df = pd.DataFrame({
                'WELL': well_name,
                'MD': md,
                'GR': gr,
                'RHOB': rhob,
                'NPHI': nphi,
                'Vp': vp,
                'Vs': vs
            })
            
            all_data.append(well_df)
        
        return pd.concat(all_data, ignore_index=True)
    
    def introduce_missing_values(self, df: pd.DataFrame, missing_percentage: float = 20.0, 
                                target_log: str = 'Vs') -> pd.DataFrame:
        """Introduce realistic missing value patterns"""
        
        df_missing = df.copy()
        log_columns = ['GR', 'RHOB', 'NPHI', 'Vp', 'Vs']
        
        # Introduce random missing values
        for col in log_columns:
            if col == target_log:
                # More missing values in target log
                missing_mask = np.random.random(len(df_missing)) < (missing_percentage / 100.0)
            else:
                # Fewer missing values in predictor logs
                missing_mask = np.random.random(len(df_missing)) < (missing_percentage / 200.0)
            
            df_missing.loc[missing_mask, col] = np.nan
        
        # Introduce some systematic missing sections (entire intervals)
        for well in df_missing['WELL'].unique():
            well_mask = df_missing['WELL'] == well
            well_indices = df_missing[well_mask].index
            
            if len(well_indices) > 100:
                # Create 1-3 missing sections per well
                n_sections = np.random.randint(1, 4)
                for _ in range(n_sections):
                    start_idx = np.random.choice(well_indices[:-50])
                    section_length = np.random.randint(20, 100)
                    end_idx = min(start_idx + section_length, well_indices[-1])
                    
                    # Randomly select which logs are missing in this section
                    missing_logs = np.random.choice(log_columns, 
                                                  size=np.random.randint(1, 3), 
                                                  replace=False)
                    
                    for log in missing_logs:
                        df_missing.loc[start_idx:end_idx, log] = np.nan
        
        return df_missing


class MLImputationFramework:
    """Comprehensive ML framework for log imputation with multiple algorithms"""
    
    def __init__(self, random_seed: int = 42):
        self.random_seed = random_seed
        self.models = {}
        self.results = {}
        self.scaler = None
        
    def get_available_models(self) -> Dict[str, Any]:
        """Get dictionary of available ML models"""
        
        models = {
            'Linear_Regression': LinearRegression(),
            'Ridge_Regression': Ridge(alpha=1.0, random_state=self.random_seed),
            'Lasso_Regression': Lasso(alpha=1.0, random_state=self.random_seed),
            'Random_Forest': RandomForestRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Extra_Trees': ExtraTreesRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Gradient_Boosting': GradientBoostingRegressor(n_estimators=100, random_state=self.random_seed)
        }
        
        # Add advanced models if available
        if XGBOOST_AVAILABLE:
            models['XGBoost'] = XGBRegressor(n_estimators=300, learning_rate=0.05, 
                                           random_state=self.random_seed, n_jobs=-1)
        
        if LIGHTGBM_AVAILABLE:
            models['LightGBM'] = LGBMRegressor(n_estimators=300, random_state=self.random_seed, n_jobs=-1)
        
        if CATBOOST_AVAILABLE:
            models['CatBoost'] = CatBoostRegressor(iterations=300, learning_rate=0.05, 
                                                 random_state=self.random_seed, verbose=False)
        
        return models
    
    def prepare_data(self, df: pd.DataFrame, target_col: str,
                    feature_cols: List[str], test_size: float = 0.25) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Prepare data for training"""

        # Get complete cases for training
        complete_mask = df[target_col].notna()
        train_data = df[complete_mask].copy()

        # Prepare features and target
        X = train_data[feature_cols].copy()
        y = train_data[target_col].values

        # Handle missing values in features using mean imputation
        imputer = SimpleImputer(strategy='mean')
        X_imputed = imputer.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_imputed, y, test_size=test_size, random_state=self.random_seed
        )

        return X_train, X_test, y_train, y_test

    def train_and_evaluate_models(self, df: pd.DataFrame, target_col: str,
                                 feature_cols: List[str], test_size: float = 0.25,
                                 use_cv: bool = True, cv_folds: int = 5) -> Dict[str, Dict[str, float]]:
        """Train and evaluate all available models"""

        print(f"3. Training and evaluating models for {target_col} imputation...")

        try:
            print(f"DEBUG: Preparing data for model training")
            print(f"DEBUG: Input data shape: {df.shape}")
            print(f"DEBUG: Target column: {target_col}")
            print(f"DEBUG: Feature columns: {feature_cols}")
            print(f"DEBUG: Test size: {test_size}, CV: {use_cv}, CV folds: {cv_folds}")

            # Prepare data
            X_train, X_test, y_train, y_test = self.prepare_data(df, target_col, feature_cols, test_size)

            print(f"   Training samples: {len(X_train)}")
            print(f"   Test samples: {len(X_test)}")
            print(f"   Features: {feature_cols}")
            print(f"DEBUG: Data preparation completed successfully")
            print()

        except Exception as e:
            print(f"ERROR: Failed to prepare data for model training: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: DataFrame info - shape: {df.shape if hasattr(df, 'shape') else 'No shape'}")
            print(f"DEBUG: DataFrame info - columns: {list(df.columns) if hasattr(df, 'columns') else 'No columns'}")
            raise

        # Get available models
        models = self.get_available_models()
        results = {}

        for model_name, model in models.items():
            print(f"   Training {model_name}...")
            print(f"DEBUG: Model type: {type(model)}")
            start_time = time.time()

            try:
                # Train model
                print(f"DEBUG: Fitting {model_name} with {len(X_train)} training samples")
                model.fit(X_train, y_train)
                print(f"DEBUG: {model_name} training completed")

                # Make predictions
                print(f"DEBUG: Making predictions with {model_name}")
                y_pred_train = model.predict(X_train)
                y_pred_test = model.predict(X_test)
                print(f"DEBUG: Predictions completed for {model_name}")

                # Calculate metrics
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)

                # Cross-validation if requested
                cv_scores = None
                if use_cv:
                    cv_scores = cross_val_score(model, X_train, y_train,
                                              cv=cv_folds, scoring='neg_mean_absolute_error')
                    cv_mae = -cv_scores.mean()
                    cv_std = cv_scores.std()
                else:
                    cv_mae = cv_std = None

                training_time = time.time() - start_time

                # Store results
                results[model_name] = {
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'cv_mae': cv_mae,
                    'cv_std': cv_std,
                    'training_time': training_time,
                    'model': model
                }

                print(f"     Test MAE: {test_mae:.3f}, Test R²: {test_r2:.3f}, Time: {training_time:.2f}s")
                if use_cv:
                    print(f"     CV MAE: {cv_mae:.3f} ± {cv_std:.3f}")

            except Exception as e:
                print(f"     Error training {model_name}: {str(e)}")
                print(f"DEBUG: Exception type for {model_name}: {type(e)}")
                print(f"DEBUG: Model parameters for {model_name}: {model.get_params() if hasattr(model, 'get_params') else 'No get_params method'}")
                results[model_name] = {'error': str(e)}

        print()
        self.results[target_col] = results
        return results

    def get_best_model(self, target_col: str, metric: str = 'test_mae') -> Tuple[str, Any]:
        """Get the best performing model for a target log"""

        if target_col not in self.results:
            raise ValueError(f"No results available for {target_col}")

        results = self.results[target_col]
        valid_results = {k: v for k, v in results.items() if 'error' not in v}

        if not valid_results:
            raise ValueError(f"No valid results for {target_col}")

        if metric == 'test_mae':
            best_model_name = min(valid_results.keys(), key=lambda k: valid_results[k]['test_mae'])
        elif metric == 'test_r2':
            best_model_name = max(valid_results.keys(), key=lambda k: valid_results[k]['test_r2'])
        else:
            raise ValueError(f"Unsupported metric: {metric}")

        best_model = valid_results[best_model_name]['model']
        return best_model_name, best_model

    def impute_missing_values(self, df: pd.DataFrame, target_col: str,
                             feature_cols: List[str], model_name: str = None) -> pd.DataFrame:
        """Impute missing values using the best or specified model"""

        if target_col not in self.results:
            raise ValueError(f"No trained models available for {target_col}")

        # Get the model to use
        if model_name is None:
            model_name, model = self.get_best_model(target_col)
            print(f"   Using best model for {target_col}: {model_name}")
        else:
            if model_name not in self.results[target_col]:
                raise ValueError(f"Model {model_name} not available for {target_col}")
            model = self.results[target_col][model_name]['model']
            print(f"   Using specified model for {target_col}: {model_name}")

        # Prepare data for imputation
        df_imputed = df.copy()
        missing_mask = df_imputed[target_col].isna()

        if not missing_mask.any():
            print(f"   No missing values found in {target_col}")
            return df_imputed

        # Get features for missing samples
        X_missing = df_imputed.loc[missing_mask, feature_cols].copy()

        # Handle missing values in features
        imputer = SimpleImputer(strategy='mean')
        X_missing_imputed = imputer.fit_transform(X_missing)

        # Make predictions
        predictions = model.predict(X_missing_imputed)

        # Fill missing values
        df_imputed.loc[missing_mask, target_col] = predictions

        print(f"   Imputed {missing_mask.sum()} missing values in {target_col}")

        return df_imputed


class ResultsAnalyzer:
    """Analyze and visualize imputation results"""

    def __init__(self):
        self.results_summary = {}

    def create_results_summary(self, ml_framework: MLImputationFramework) -> pd.DataFrame:
        """Create a comprehensive summary of all model results"""

        summary_data = []

        for target_log, results in ml_framework.results.items():
            for model_name, metrics in results.items():
                if 'error' not in metrics:
                    row = {
                        'Target_Log': target_log,
                        'Model': model_name,
                        'Train_MAE': metrics['train_mae'],
                        'Test_MAE': metrics['test_mae'],
                        'Train_RMSE': metrics['train_rmse'],
                        'Test_RMSE': metrics['test_rmse'],
                        'Train_R2': metrics['train_r2'],
                        'Test_R2': metrics['test_r2'],
                        'CV_MAE': metrics.get('cv_mae', np.nan),
                        'CV_STD': metrics.get('cv_std', np.nan),
                        'Training_Time': metrics['training_time']
                    }
                    summary_data.append(row)

        summary_df = pd.DataFrame(summary_data)
        self.results_summary = summary_df
        return summary_df

    def print_results_table(self, ml_framework: MLImputationFramework):
        """Print a formatted results table"""

        summary_df = self.create_results_summary(ml_framework)

        if summary_df.empty:
            print("No results to display.")
            return

        print("4. Model Performance Summary")
        print("="*100)

        for target_log in summary_df['Target_Log'].unique():
            target_results = summary_df[summary_df['Target_Log'] == target_log].copy()
            target_results = target_results.sort_values('Test_MAE')

            print(f"\nTarget Log: {target_log}")
            print("-" * 90)
            print(f"{'Model':<20} {'Test MAE':<10} {'Test R²':<10} {'CV MAE':<12} {'Time (s)':<10}")
            print("-" * 90)

            for _, row in target_results.iterrows():
                cv_mae_str = f"{row['CV_MAE']:.3f}" if not pd.isna(row['CV_MAE']) else "N/A"
                print(f"{row['Model']:<20} {row['Test_MAE']:<10.3f} {row['Test_R2']:<10.3f} "
                      f"{cv_mae_str:<12} {row['Training_Time']:<10.2f}")

            # Highlight best model
            best_model = target_results.iloc[0]
            print(f"\nBest model for {target_log}: {best_model['Model']} "
                  f"(MAE: {best_model['Test_MAE']:.3f}, R²: {best_model['Test_R2']:.3f})")

        print("\n" + "="*100)

    def plot_model_comparison(self, ml_framework: MLImputationFramework, save_plots: bool = True):
        """Create comparison plots for model performance"""

        summary_df = self.create_results_summary(ml_framework)

        if summary_df.empty:
            print("No results to plot.")
            return

        # Set up the plotting style
        plt.style.use('default')
        if SEABORN_AVAILABLE:
            sns.set_palette("husl")

        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')

        # Plot 1: Test MAE comparison
        ax1 = axes[0, 0]
        for target_log in summary_df['Target_Log'].unique():
            target_data = summary_df[summary_df['Target_Log'] == target_log]
            ax1.bar([f"{row['Model']}\n({target_log})" for _, row in target_data.iterrows()],
                   target_data['Test_MAE'], alpha=0.7, label=target_log)

        ax1.set_title('Test Mean Absolute Error (MAE)')
        ax1.set_ylabel('MAE')
        ax1.tick_params(axis='x', rotation=45)
        ax1.legend()

        # Plot 2: Test R² comparison
        ax2 = axes[0, 1]
        for target_log in summary_df['Target_Log'].unique():
            target_data = summary_df[summary_df['Target_Log'] == target_log]
            ax2.bar([f"{row['Model']}\n({target_log})" for _, row in target_data.iterrows()],
                   target_data['Test_R2'], alpha=0.7, label=target_log)

        ax2.set_title('Test R² Score')
        ax2.set_ylabel('R²')
        ax2.tick_params(axis='x', rotation=45)
        ax2.legend()

        # Plot 3: Training time comparison
        ax3 = axes[1, 0]
        models = summary_df['Model'].unique()
        avg_times = [summary_df[summary_df['Model'] == model]['Training_Time'].mean() for model in models]
        bars = ax3.bar(models, avg_times, alpha=0.7)
        ax3.set_title('Average Training Time')
        ax3.set_ylabel('Time (seconds)')
        ax3.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, time_val in zip(bars, avg_times):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{time_val:.2f}s', ha='center', va='bottom')

        # Plot 4: MAE vs R² scatter
        ax4 = axes[1, 1]
        for target_log in summary_df['Target_Log'].unique():
            target_data = summary_df[summary_df['Target_Log'] == target_log]
            ax4.scatter(target_data['Test_MAE'], target_data['Test_R2'],
                       alpha=0.7, s=100, label=target_log)

            # Add model labels
            for _, row in target_data.iterrows():
                ax4.annotate(row['Model'], (row['Test_MAE'], row['Test_R2']),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

        ax4.set_title('MAE vs R² Trade-off')
        ax4.set_xlabel('Test MAE')
        ax4.set_ylabel('Test R²')
        ax4.legend()

        plt.tight_layout()

        if save_plots:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'model_comparison_{timestamp}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"   Model comparison plot saved as: {filename}")

        plt.show()

    def plot_data_coverage(self, df: pd.DataFrame, save_plots: bool = True):
        """Plot data coverage statistics"""

        log_columns = ['GR', 'RHOB', 'NPHI', 'Vp', 'Vs']

        # Calculate coverage by well
        coverage_by_well = []
        for well in df['WELL'].unique():
            well_data = df[df['WELL'] == well]
            well_coverage = {}
            well_coverage['WELL'] = well
            for col in log_columns:
                coverage = (1 - well_data[col].isna().mean()) * 100
                well_coverage[col] = coverage
            coverage_by_well.append(well_coverage)

        coverage_df = pd.DataFrame(coverage_by_well)

        # Create coverage plots
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('Data Coverage Analysis', fontsize=16, fontweight='bold')

        # Plot 1: Overall coverage
        ax1 = axes[0]
        overall_coverage = [(1 - df[col].isna().mean()) * 100 for col in log_columns]
        bars = ax1.bar(log_columns, overall_coverage, alpha=0.7, color='skyblue')
        ax1.set_title('Overall Data Coverage by Log Type')
        ax1.set_ylabel('Coverage (%)')
        ax1.set_ylim(0, 100)

        # Add value labels
        for bar, coverage in zip(bars, overall_coverage):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{coverage:.1f}%', ha='center', va='bottom')

        # Plot 2: Coverage heatmap by well
        ax2 = axes[1]
        coverage_matrix = coverage_df.set_index('WELL')[log_columns].values
        im = ax2.imshow(coverage_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=100)
        ax2.set_title('Data Coverage by Well')
        ax2.set_xlabel('Log Type')
        ax2.set_ylabel('Well')
        ax2.set_xticks(range(len(log_columns)))
        ax2.set_xticklabels(log_columns)
        ax2.set_yticks(range(len(coverage_df)))
        ax2.set_yticklabels(coverage_df['WELL'], fontsize=8)

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax2)
        cbar.set_label('Coverage (%)')

        plt.tight_layout()

        if save_plots:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'data_coverage_{timestamp}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"   Data coverage plot saved as: {filename}")

        plt.show()


class AutoMLFramework:
    """TPOT AutoML integration for automated model selection and hyperparameter tuning"""

    def __init__(self, random_seed: int = 42):
        self.random_seed = random_seed
        self.automl_models = {}

    def get_automl_models(self, time_limit: int = 300) -> Dict[str, Any]:
        """Get available AutoML models"""

        models = {}

        if TPOT_AVAILABLE:
            models['TPOT'] = TPOTRegressor(
                generations=5,
                population_size=20,
                max_time_mins=max(1, time_limit//60),  # Ensure at least 1 minute
                random_state=self.random_seed,
                verbosity=0,
                n_jobs=1,
                cv=3  # Use 3-fold CV for faster training
            )

        return models

    def train_automl_models(self, X_train: np.ndarray, y_train: np.ndarray,
                           X_test: np.ndarray, y_test: np.ndarray,
                           time_limit: int = 300) -> Dict[str, Dict[str, float]]:
        """Train AutoML models and return results"""

        automl_models = self.get_automl_models(time_limit)
        results = {}

        if not automl_models:
            print("   No AutoML frameworks available (TPOT not installed)")
            return results

        print(f"   Training TPOT AutoML model (time limit: {time_limit}s)...")

        for model_name, model in automl_models.items():
            print(f"     Training {model_name}...")
            print(f"DEBUG: AutoML model type: {type(model)}")
            print(f"DEBUG: AutoML model parameters: {model.get_params() if hasattr(model, 'get_params') else 'No get_params method'}")
            start_time = time.time()

            try:
                # Train AutoML model
                print(f"DEBUG: Starting {model_name} fit with {len(X_train)} samples and {X_train.shape[1]} features")
                model.fit(X_train, y_train)
                print(f"DEBUG: {model_name} training completed")

                # Make predictions
                print(f"DEBUG: Making predictions with {model_name}")
                y_pred_train = model.predict(X_train)
                y_pred_test = model.predict(X_test)
                print(f"DEBUG: {model_name} predictions completed")

                # Calculate metrics
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)

                training_time = time.time() - start_time

                # Store results
                results[model_name] = {
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'training_time': training_time,
                    'model': model
                }

                print(f"       Test MAE: {test_mae:.3f}, Test R²: {test_r2:.3f}, Time: {training_time:.2f}s")

                # Store the trained model
                self.automl_models[model_name] = model

            except Exception as e:
                print(f"       Error training {model_name}: {str(e)}")
                print(f"DEBUG: AutoML exception type for {model_name}: {type(e)}")
                print(f"DEBUG: AutoML training time before error: {time.time() - start_time:.2f}s")
                if hasattr(model, 'fitted_pipeline_'):
                    print(f"DEBUG: Fitted pipeline available: {model.fitted_pipeline_}")
                else:
                    print(f"DEBUG: No fitted pipeline available")
                results[model_name] = {'error': str(e)}

        return results


def main():
    """Main execution function"""

    print("="*80)
    print("MISSING LOG IMPUTATION WITH MULTIPLE ML ALGORITHMS")
    print("="*80)
    print(f"Execution started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Get parameters (in standalone mode, use defaults)
    try:
        if 'parameters' in globals():
            print("DEBUG: Using parameters from global scope")
            print(f"DEBUG: Parameters type: {type(parameters)}")
            print(f"DEBUG: Parameters content: {parameters}")

            # Validate parameters dictionary
            required_params = ['n_wells', 'samples_per_well', 'missing_percentage', 'target_log',
                             'use_automl', 'generate_plots', 'random_seed', 'test_size',
                             'cross_validation', 'cv_folds']

            for param in required_params:
                if param not in parameters:
                    raise KeyError(f"Required parameter '{param}' not found in parameters")

            n_wells = parameters['n_wells']
            samples_per_well = parameters['samples_per_well']
            missing_percentage = parameters['missing_percentage']
            target_log_idx = parameters['target_log']
            use_automl = parameters['use_automl']
            generate_plots = parameters['generate_plots']
            random_seed = parameters['random_seed']
            test_size = parameters['test_size']
            cross_validation = parameters['cross_validation']
            cv_folds = parameters['cv_folds']

            print("DEBUG: Successfully extracted all parameters from global scope")
        else:
            print("DEBUG: Using default parameters for standalone execution")
            # Default parameters for standalone execution
            n_wells = 20
            samples_per_well = 500
            missing_percentage = 20.0
            target_log_idx = 0
            use_automl = True  # Enabled by default in standalone mode (using TPOT)
            generate_plots = True
            random_seed = 42
            test_size = 0.25
            cross_validation = True
            cv_folds = 5
            print("DEBUG: Default parameters set successfully")

    except Exception as e:
        print(f"ERROR: Failed to process parameters: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        if 'parameters' in globals():
            print(f"DEBUG: Parameters variable exists with type: {type(parameters)}")
            print(f"DEBUG: Parameters content: {parameters}")
        else:
            print("DEBUG: Parameters variable not found in global scope")
        raise

    # Validate target log index
    target_logs = ['Vs', 'Vp', 'RHOB', 'NPHI', 'GR']
    try:
        if not isinstance(target_log_idx, int) or target_log_idx < 0 or target_log_idx >= len(target_logs):
            raise ValueError(f"Invalid target_log_idx: {target_log_idx}. Must be 0-{len(target_logs)-1}")
        target_log = target_logs[target_log_idx]
        print(f"DEBUG: Target log selected: {target_log} (index: {target_log_idx})")
    except Exception as e:
        print(f"ERROR: Failed to select target log: {str(e)}")
        print(f"DEBUG: target_log_idx type: {type(target_log_idx)}, value: {target_log_idx}")
        print(f"DEBUG: Available target logs: {target_logs}")
        raise

    print(f"Configuration:")
    print(f"  - Number of wells: {n_wells}")
    print(f"  - Samples per well: {samples_per_well}")
    print(f"  - Missing data percentage: {missing_percentage}%")
    print(f"  - Target log for imputation: {target_log}")
    print(f"  - Random seed: {random_seed}")
    print(f"  - Test set size: {test_size}")
    print(f"  - Cross validation: {cross_validation}")
    if cross_validation:
        print(f"  - CV folds: {cv_folds}")
    print(f"  - AutoML enabled: {use_automl}")
    print(f"  - Generate plots: {generate_plots}")
    print()

    # Debug: Check available libraries
    print("DEBUG: Checking available ML libraries...")
    print(f"  - XGBoost available: {XGBOOST_AVAILABLE}")
    print(f"  - LightGBM available: {LIGHTGBM_AVAILABLE}")
    print(f"  - CatBoost available: {CATBOOST_AVAILABLE}")
    print(f"  - TPOT available: {TPOT_AVAILABLE}")
    print(f"  - Seaborn available: {SEABORN_AVAILABLE}")
    print(f"  - Petrel available: {PETREL_AVAILABLE}")
    print()

    # Generate synthetic data
    print("1. Generating synthetic well log data...")
    try:
        print(f"DEBUG: Creating SyntheticWellLogGenerator with random_seed={random_seed}")
        generator = SyntheticWellLogGenerator(random_seed=random_seed)
        print("DEBUG: SyntheticWellLogGenerator created successfully")

        # Generate complete dataset
        print(f"DEBUG: Generating {n_wells} wells with {samples_per_well} samples each")
        complete_data = generator.generate_well_logs(n_wells=n_wells, samples_per_well=samples_per_well)

        # Validate generated data
        if complete_data is None or len(complete_data) == 0:
            raise ValueError("Generated dataset is empty")

        print(f"   Generated {len(complete_data)} log samples across {n_wells} wells")
        print(f"DEBUG: Complete data shape: {complete_data.shape}")
        print(f"DEBUG: Complete data columns: {list(complete_data.columns)}")
        print(f"DEBUG: Wells generated: {complete_data['WELL'].nunique()}")

        # Introduce missing values
        print(f"DEBUG: Introducing {missing_percentage}% missing values for target log: {target_log}")
        data_with_missing = generator.introduce_missing_values(
            complete_data, missing_percentage=missing_percentage, target_log=target_log
        )

        # Validate missing data
        if data_with_missing is None or len(data_with_missing) == 0:
            raise ValueError("Dataset with missing values is empty")

        print(f"DEBUG: Data with missing values shape: {data_with_missing.shape}")

    except Exception as e:
        print(f"ERROR: Failed to generate synthetic data: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Parameters - n_wells: {n_wells}, samples_per_well: {samples_per_well}")
        print(f"DEBUG: Parameters - missing_percentage: {missing_percentage}, target_log: {target_log}")
        raise

    # Print data coverage statistics
    print("DEBUG: Calculating data coverage statistics...")
    log_columns = ['GR', 'RHOB', 'NPHI', 'Vp', 'Vs']
    coverage_stats = {}
    try:
        for col in log_columns:
            if col not in data_with_missing.columns:
                print(f"WARNING: Column {col} not found in dataset")
                continue
            coverage = (1 - data_with_missing[col].isna().mean()) * 100
            coverage_stats[col] = coverage
            missing_count = data_with_missing[col].isna().sum()
            total_count = len(data_with_missing[col])
            print(f"   {col} coverage: {coverage:.1f}% ({missing_count}/{total_count} missing)")

        print(f"DEBUG: Coverage statistics calculated for {len(coverage_stats)} columns")
    except Exception as e:
        print(f"ERROR: Failed to calculate coverage statistics: {str(e)}")
        print(f"DEBUG: data_with_missing type: {type(data_with_missing)}")
        print(f"DEBUG: data_with_missing columns: {list(data_with_missing.columns) if hasattr(data_with_missing, 'columns') else 'No columns attribute'}")
        raise

    print()

    # Initialize ML framework
    print("2. Initializing ML imputation framework...")
    try:
        print(f"DEBUG: Creating MLImputationFramework with random_seed={random_seed}")
        ml_framework = MLImputationFramework(random_seed=random_seed)
        print("DEBUG: MLImputationFramework created successfully")

        available_models = ml_framework.get_available_models()
        print(f"   Available models: {list(available_models.keys())}")
        print(f"DEBUG: {len(available_models)} models available for training")

    except Exception as e:
        print(f"ERROR: Failed to initialize ML framework: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        raise

    print()

    # Define feature columns (all logs except the target)
    try:
        feature_cols = [col for col in log_columns if col != target_log] + ['MD']
        print(f"DEBUG: Feature columns defined: {feature_cols}")

        # Validate feature columns exist in dataset
        missing_features = [col for col in feature_cols if col not in data_with_missing.columns]
        if missing_features:
            print(f"WARNING: Missing feature columns in dataset: {missing_features}")
            feature_cols = [col for col in feature_cols if col in data_with_missing.columns]
            print(f"DEBUG: Updated feature columns: {feature_cols}")

        if len(feature_cols) == 0:
            raise ValueError("No valid feature columns available for training")

    except Exception as e:
        print(f"ERROR: Failed to define feature columns: {str(e)}")
        print(f"DEBUG: target_log: {target_log}")
        print(f"DEBUG: log_columns: {log_columns}")
        print(f"DEBUG: Available columns: {list(data_with_missing.columns)}")
        raise

    # Train and evaluate models
    try:
        print(f"DEBUG: Starting model training with {len(feature_cols)} features")
        print(f"DEBUG: Training parameters - test_size: {test_size}, cv: {cross_validation}, cv_folds: {cv_folds}")

        results = ml_framework.train_and_evaluate_models(
            data_with_missing, target_log, feature_cols,
            test_size=test_size, use_cv=cross_validation, cv_folds=cv_folds
        )

        print(f"DEBUG: Model training completed. {len(results)} models trained")

    except Exception as e:
        print(f"ERROR: Failed to train and evaluate models: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Data shape: {data_with_missing.shape}")
        print(f"DEBUG: Target log: {target_log}")
        print(f"DEBUG: Feature columns: {feature_cols}")
        raise

    # Train TPOT AutoML model if enabled
    if use_automl:
        print("   Training TPOT AutoML model...")
        try:
            print(f"DEBUG: Creating AutoMLFramework with random_seed={random_seed}")
            automl_framework = AutoMLFramework(random_seed=random_seed)
            print("DEBUG: AutoMLFramework created successfully")

            # Check if TPOT is available
            if not TPOT_AVAILABLE:
                print("WARNING: TPOT not available, skipping AutoML training")
            else:
                print("DEBUG: TPOT is available, proceeding with AutoML training")

                # Prepare data for AutoML
                print("DEBUG: Preparing data for AutoML training")
                X_train, X_test, y_train, y_test = ml_framework.prepare_data(
                    data_with_missing, target_log, feature_cols, test_size
                )

                print(f"DEBUG: AutoML data prepared - X_train: {X_train.shape}, X_test: {X_test.shape}")
                print(f"DEBUG: AutoML data prepared - y_train: {len(y_train)}, y_test: {len(y_test)}")

                # Train TPOT AutoML model
                print("DEBUG: Starting TPOT AutoML training (this may take several minutes)")
                automl_results = automl_framework.train_automl_models(
                    X_train, y_train, X_test, y_test, time_limit=300
                )

                # Merge TPOT results with regular ML results
                if automl_results:
                    print(f"DEBUG: AutoML training completed with {len(automl_results)} models")
                    ml_framework.results[target_log].update(automl_results)
                    print(f"   Added {len(automl_results)} TPOT AutoML model(s) to comparison")
                else:
                    print("WARNING: No AutoML results returned")

        except Exception as e:
            print(f"ERROR: Failed to train AutoML models: {str(e)}")
            print(f"DEBUG: Exception type: {type(e)}")
            print(f"DEBUG: TPOT_AVAILABLE: {TPOT_AVAILABLE}")
            print("WARNING: Continuing without AutoML results")

        print()

    # Initialize results analyzer
    analyzer = ResultsAnalyzer()

    # Print results summary
    analyzer.print_results_table(ml_framework)

    # Generate visualizations if requested
    if generate_plots:
        print("\n5. Generating visualizations...")

        # Plot data coverage
        print("   Creating data coverage plots...")
        analyzer.plot_data_coverage(data_with_missing, save_plots=True)

        # Plot model comparison
        print("   Creating model comparison plots...")
        analyzer.plot_model_comparison(ml_framework, save_plots=True)

    # Perform imputation with best model
    print("\n6. Performing imputation with best model...")
    try:
        print("DEBUG: Getting best model for imputation")
        best_model_name, best_model = ml_framework.get_best_model(target_log)
        print(f"DEBUG: Best model selected: {best_model_name}")
        print(f"DEBUG: Best model type: {type(best_model)}")

        # Impute missing values
        print("DEBUG: Starting imputation process")
        imputed_data = ml_framework.impute_missing_values(
            data_with_missing, target_log, feature_cols
        )

        # Validate imputed data
        if imputed_data is None:
            raise ValueError("Imputation returned None")
        if len(imputed_data) == 0:
            raise ValueError("Imputed data is empty")

        print(f"DEBUG: Imputation completed. Data shape: {imputed_data.shape}")

    except Exception as e:
        print(f"ERROR: Failed to perform imputation: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Available results: {list(ml_framework.results.keys()) if hasattr(ml_framework, 'results') else 'No results'}")
        if target_log in ml_framework.results:
            print(f"DEBUG: Models for {target_log}: {list(ml_framework.results[target_log].keys())}")
        raise

    # Calculate imputation statistics
    try:
        print("DEBUG: Calculating imputation statistics")
        original_missing = data_with_missing[target_log].isna().sum()
        original_present = data_with_missing[target_log].notna().sum()
        final_present = imputed_data[target_log].notna().sum()
        final_missing = imputed_data[target_log].isna().sum()
        imputed_values = final_present - original_present

        print(f"   Successfully imputed {imputed_values} missing values")
        print(f"   Original missing values: {original_missing}")
        print(f"   Remaining missing values: {final_missing}")
        print(f"DEBUG: Original present: {original_present}, Final present: {final_present}")
        print(f"DEBUG: Total data points: {len(imputed_data[target_log])}")

        # Validate imputation success
        if imputed_values != original_missing - final_missing:
            print(f"WARNING: Imputation count mismatch. Expected: {original_missing - final_missing}, Got: {imputed_values}")

    except Exception as e:
        print(f"ERROR: Failed to calculate imputation statistics: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: data_with_missing[{target_log}] type: {type(data_with_missing[target_log]) if target_log in data_with_missing.columns else 'Column not found'}")
        print(f"DEBUG: imputed_data[{target_log}] type: {type(imputed_data[target_log]) if target_log in imputed_data.columns else 'Column not found'}")
        raise

    # Calculate accuracy on known values (where we artificially introduced missing values)
    if 'complete_data' in locals():
        # Compare imputed values with original complete data
        artificial_missing_mask = data_with_missing[target_log].isna() & complete_data[target_log].notna()

        if artificial_missing_mask.any():
            original_values = complete_data.loc[artificial_missing_mask, target_log]
            imputed_values_subset = imputed_data.loc[artificial_missing_mask, target_log]

            imputation_mae = mean_absolute_error(original_values, imputed_values_subset)
            imputation_r2 = r2_score(original_values, imputed_values_subset)

            print(f"   Imputation accuracy on artificially missing data:")
            print(f"     MAE: {imputation_mae:.3f}")
            print(f"     R²: {imputation_r2:.3f}")

    # Save results
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        print(f"DEBUG: Saving results with timestamp: {timestamp}")

        # Save imputed data
        output_filename = f'imputed_well_logs_{target_log}_{timestamp}.csv'
        print(f"DEBUG: Saving imputed data to: {output_filename}")
        imputed_data.to_csv(output_filename, index=False)
        print(f"\n   Imputed data saved as: {output_filename}")

        # Save results summary
        print("DEBUG: Creating results summary")
        summary_df = analyzer.create_results_summary(ml_framework)
        summary_filename = f'model_results_summary_{target_log}_{timestamp}.csv'
        print(f"DEBUG: Saving results summary to: {summary_filename}")
        summary_df.to_csv(summary_filename, index=False)
        print(f"   Results summary saved as: {summary_filename}")

        print(f"DEBUG: Results summary shape: {summary_df.shape}")
        print(f"DEBUG: Results summary columns: {list(summary_df.columns)}")

    except Exception as e:
        print(f"ERROR: Failed to save results: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        print(f"DEBUG: Current working directory: {os.getcwd() if 'os' in globals() else 'os not imported'}")
        # Don't raise here, as the main computation is complete
        print("WARNING: Continuing despite save error")

    print("\n" + "="*80)
    print("IMPUTATION WORKFLOW COMPLETED SUCCESSFULLY")
    print("="*80)
    print(f"Execution completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"DEBUG: Final return data types:")
    print(f"  - imputed_data: {type(imputed_data)}")
    print(f"  - results_summary: {type(summary_df) if 'summary_df' in locals() else 'Not created'}")
    print(f"  - best_model: {best_model_name} ({type(best_model)})")
    print(f"  - ml_framework: {type(ml_framework)}")

    return {
        'imputed_data': imputed_data,
        'results_summary': summary_df if 'summary_df' in locals() else None,
        'best_model': best_model_name,
        'ml_framework': ml_framework
    }


if __name__ == "__main__":
    main()
